#include "../include/detection_processor.h"
#include "../include/data_type.h"
#include "../include/logger.h"
#include <iostream>
#include <iomanip>
#include <cmath>
#include <tuple>
#include <stdexcept>
#include <limits>
#include <set>
#include <algorithm>

void DetectionProcessor::setSizeRangesConfigFromJson(const std::string& json_file) {
    try {
        size_ranges_config_ = SimpleJson::parseFile(json_file);
    } catch (const std::exception& e) {
        LOG_ERROR("加载JSON配置文件失败:", e.what());
        // 使用默认配置
        size_ranges_config_ = SimpleJson(SimpleJson::NULL_TYPE);
    }
}

void DetectionProcessor::setSizeRangesConfig(const SimpleJson& config) {
    size_ranges_config_ = config;
}

#ifdef HAVE_YAML_CPP
void DetectionProcessor::setSizeRangesConfig(const YAML::Node& config) {
    yaml_size_ranges_config_ = config;
}
#endif

void DetectionProcessor::initializeDistanceTable(const std::string& table_path, std::shared_ptr<AppConfig> config) {
    pixel_converter_ = std::make_unique<PixelConverter>(table_path, config);
    app_config_ = config;  // 保存AppConfig引用以便访问class_names
}

bool DetectionProcessor::initializeCameraIntrinsics(const std::string& intrinsics_file) {
    camera_intrinsics_ = std::make_unique<CameraIntrinsics>();
    bool success = false;

    // 根据文件扩展名判断是YAML还是JSON
    std::string ext = intrinsics_file.substr(intrinsics_file.find_last_of(".") + 1);
    if (ext == "json") {
        LOG_INFO("检测到JSON格式的相机内参文件");
        success = camera_intrinsics_->loadFromJsonFile(intrinsics_file);
    } else if (ext == "yaml" || ext == "yml") {
        LOG_INFO("检测到YAML格式的相机内参文件");
        success = camera_intrinsics_->loadFromYamlFile(intrinsics_file);
    } else {
        LOG_ERROR("不支持的相机内参文件格式:", ext);
        success = false;
    }

    if (success) {
        LOG_INFO("相机内参初始化成功");
    } else {
        LOG_ERROR("相机内参初始化失败");
        camera_intrinsics_.reset();
    }
    return success;
}



bool DetectionProcessor::isValidCoordinate(double x, double y) const {
    // 检查是否为无效区域标记 (0.000, 0.490) 或类似的值
    return !(std::abs(x) < 0.01 || std::abs(std::abs(y) - 0.49) < 0.1);
}

std::pair<double, double> DetectionProcessor::findNearestValidPoint(int pixel_x, int pixel_y, int max_search_radius) {
    if (!pixel_converter_) {
        LOG_ERROR("PixelConverter 未初始化");
        return std::make_pair(std::numeric_limits<double>::quiet_NaN(), std::numeric_limits<double>::quiet_NaN());
    }
    LOG_DEBUG("搜索像素点 (", pixel_x, ",", pixel_y, ") 周围的有效坐标, 最大半径:", max_search_radius);

    for (int radius = 1; radius <= max_search_radius; ++radius) {
        // 搜索以当前点为中心的正方形区域
        for (int dx = -radius; dx <= radius; ++dx) {
            for (int dy = -radius; dy <= radius; ++dy) {
                // 只检查边界点，避免重复搜索内部点
                if (std::abs(dx) != radius && std::abs(dy) != radius) {
                    continue;
                }

                int search_x = pixel_x + dx;
                int search_y = pixel_y + dy;

                // 检查搜索点是否在有效范围内
                if (pixel_converter_->isValidPixelCoordinate(search_x, search_y)) {
                    try {
                        auto [phys_x, phys_y] = pixel_converter_->queryPhysicalLocation(search_x, search_y);
                        if (isValidCoordinate(phys_x, phys_y)) {
                            LOG_DEBUG("找到有效点: 像素(", search_x, ",", search_y, ") -> 物理(", phys_x, ",", phys_y, "), 搜索半径:", radius);
                            return std::make_pair(phys_x, phys_y);
                        }
                    } catch (...) {
                        continue;
                    }
                }
            }
        }
    }
    LOG_WARNING("在半径", max_search_radius, "内未找到有效坐标点");
    return std::make_pair(std::numeric_limits<double>::quiet_NaN(), std::numeric_limits<double>::quiet_NaN());
}

std::pair<double, double> DetectionProcessor::getRobustPhysicalCoordinate(int pixel_x, int pixel_y) {
    if (!pixel_converter_) {
        LOG_ERROR("PixelConverter 未初始化");
        return std::make_pair(std::numeric_limits<double>::quiet_NaN(), std::numeric_limits<double>::quiet_NaN());
    }
    try {
        // 首先尝试直接查询
        auto [phys_x, phys_y] = pixel_converter_->queryPhysicalLocation(pixel_x, pixel_y);

        if (isValidCoordinate(phys_x, phys_y)) {
            return std::make_pair(phys_x, phys_y);
        }

        // LOG_WARNING("像素点 (", pixel_x, ",", pixel_y, ") 在无效标定区域，物理坐标: (", phys_x, ",", phys_y, ")");

        // 策略1: 搜索最近的有效点
        auto [valid_x, valid_y] = findNearestValidPoint(pixel_x, pixel_y);
        if (!std::isnan(valid_x)) {
            // LOG_WARNING("使用最近的有效点: (", valid_x, ",", valid_y, ")");
            return std::make_pair(valid_x, valid_y);
        } else {
            return std::make_pair(std::numeric_limits<double>::quiet_NaN(), std::numeric_limits<double>::quiet_NaN());
        }

    } catch (const std::exception& e) {
        LOG_ERROR("获取物理坐标失败:", e.what());
        return std::make_pair(std::numeric_limits<double>::quiet_NaN(), std::numeric_limits<double>::quiet_NaN());
    }
}

std::tuple<double, double, double> DetectionProcessor::calculateTargetSize(const BBox& bbox) {
    if (!pixel_converter_) {
        LOG_ERROR("PixelConverter 未初始化");
        return std::make_tuple(0.0, 0.0, 0.0);
    }
    try {
        // 使用鲁棒方法获取物理坐标
        auto [z1, y1] = getRobustPhysicalCoordinate(bbox.xmin, bbox.ymax);  // bottom left
        auto [z2, y2] = getRobustPhysicalCoordinate(bbox.xmax, bbox.ymax);  // bottom right

        // 计算长度
        double bottom_length = std::sqrt(std::pow(z2 - z1, 2) + std::pow(y2 - y1, 2));

        LOG_DEBUG("左下角像素坐标 (", bbox.xmin, ",", bbox.ymax, ") -> 物理坐标 (", z1, ",", y1, ")");
        LOG_DEBUG("右下角像素坐标 (", bbox.xmax, ",", bbox.ymax, ") -> 物理坐标 (", z2, ",", y2, ")");
        LOG_DEBUG("下边长度:", bottom_length, "cm");

            return std::make_tuple(bottom_length, z1, z2);

    } catch (const std::exception& e) {
        LOG_ERROR("计算目标尺寸失败:", e.what());
        return std::make_tuple(0.0, 0.0, 0.0);
    }
}

bool DetectionProcessor::isSizeReasonableForLabel(const std::string& label, double size_y) const {
    try {
        // 首先尝试使用JSON配置
        if (!size_ranges_config_.isNull() && size_ranges_config_.hasKey("objects")) {
            const SimpleJson& objects = size_ranges_config_["objects"];
            const SimpleJson* object_config = nullptr;

            if (objects.hasKey(label)) {
                object_config = &objects[label];
            } else if (size_ranges_config_.hasKey("default")) {
                object_config = &size_ranges_config_["default"];
            }

            if (object_config && !object_config->isNull()) {
                double config_max_size = (*object_config)["max_size"].asDouble();
                double config_min_size = (*object_config)["min_size"].asDouble();
                std::string description = (*object_config)["description"].asString();

                LOG_DEBUG(label, "长度:", size_y, "cm, 配置范围:", config_min_size, "-", config_max_size, "cm");

                if (size_y > config_max_size || size_y < config_min_size) {
                    LOG_WARNING(description);
                    return false;
                }
                return true;
            }
        }

#ifdef HAVE_YAML_CPP
        // 如果JSON配置不可用，尝试YAML配置
        if (yaml_size_ranges_config_["objects"][label]) {
            YAML::Node object_config = yaml_size_ranges_config_["objects"][label];
            double config_max_size = object_config["max_size"].as<double>();
            double config_min_size = object_config["min_size"].as<double>();
            std::string description = object_config["description"].as<std::string>();

            LOG_DEBUG(label, "长度:", size_y, "cm (YAML配置检查), 范围:", config_min_size, "-", config_max_size);

            if (size_y > config_max_size || size_y < config_min_size) {
                LOG_WARNING(description);
                return false;
            }
            return true;
        } else if (yaml_size_ranges_config_["default"]) {
            YAML::Node object_config = yaml_size_ranges_config_["default"];
            double config_max_size = object_config["max_size"].as<double>();
            double config_min_size = object_config["min_size"].as<double>();
            std::string description = object_config["description"].as<std::string>();

            if (size_y > config_max_size || size_y < config_min_size) {
                LOG_WARNING(label, "长度:", size_y, "cm,", description);
                return false;
            }
            return true;
        }
#endif

        // 如果没有配置文件，使用默认的尺寸检查逻辑
        LOG_DEBUG(label, "长度:", size_y, "cm (使用默认检查)");

        // 简单的默认范围检查
        if (size_y > 200.0 || size_y < 5.0) {
            LOG_WARNING("物体尺寸超出合理范围 (5-200cm)");
            return false;
        }
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR("检查尺寸范围失败:", e.what());
        // 出错时使用默认检查
        if (size_y > 200.0 || size_y < 5.0) {
            return false;
        }
        return true;
    }
}

std::tuple<bool, double, double, double, std::string> DetectionProcessor::isDetectionReasonable(DetectionResult& det, DetectionType type) {
    if (!pixel_converter_) {
        LOG_ERROR("PixelConverter 未初始化");
        return std::make_tuple(false, 0.0, 0.0, 0.0, "converter not initialized");
    }

    // 性能优化：预计算边界框尺寸，避免重复计算
    const int bbox_width = det.bbox_flag.box.xmax - det.bbox_flag.box.xmin;
    const int bbox_height = det.bbox_flag.box.ymax - det.bbox_flag.box.ymin;

    // 检查检测框是否在图像范围内
    if (!pixel_converter_->isValidPixelCoordinate(det.bbox_flag.box.xmin, det.bbox_flag.box.ymax) ||
        !pixel_converter_->isValidPixelCoordinate(det.bbox_flag.box.xmax, det.bbox_flag.box.ymax)) {
        // LOG_WARNING("检测框超出测距范围");
        return std::make_tuple(false, 0.0, 0.0, 0.0, "out of image range");
    }

    // // 性能优化：使用预计算的尺寸，避免重复减法运算
    // const int box_area = bbox_width * bbox_height;
    // if (box_area < 10) {
    //     LOG_WARNING("检测框面积过小 (", box_area, "像素),不进行测距");
    //     return std::make_tuple(false, 0.0, 0.0, 0.0, "box area is small");
    // }

    // 计算目标尺寸
    LOG_DEBUG("开始计算目标尺寸...");
    auto [size_y, z1, z2] = calculateTargetSize(det.bbox_flag.box);
    LOG_DEBUG("目标尺寸计算完成: size_y=", size_y, ", z1=", z1, ", z2=", z2);

    // 性能优化：缓存距离值，避免重复访问
    const double distance = z1;
    const int x_max = pixel_converter_->getXMax(); // 缓存配置值

    if (distance >= x_max) {
        LOG_WARNING("目标距离过远 (", distance, "cm)");
        return std::make_tuple(false, 0.0, 0.0, 0.0, "distance far");
    }

    // 打印目标尺寸
    // std::cout << "\n目标尺寸:" << std::endl;
    // std::cout << "长度: " << std::fixed << std::setprecision(3) << size_y << " cm" << std::endl;

    // 检查尺寸与标签的匹配
    // 将int类型的label转换为string类型
    std::string label_str = "";
    if (app_config_ && det.bbox_flag.box.label >= 0 && det.bbox_flag.box.label < static_cast<int>(app_config_->class_names.size())) {
        label_str = app_config_->class_names[det.bbox_flag.box.label];
    }
    
    if (type == DetectionType::BOX_DETECTION) {
        if (!isSizeReasonableForLabel(label_str, size_y)) {
            return std::make_tuple(false, 0.0, 0.0, 0.0, "size false");
        }
    }
    // 性能优化：使用空字符串字面量，避免临时对象创建
    return std::make_tuple(true, z1, z2, size_y, "");
}

bool DetectionProcessor::convertPixelToCameraCoordinates(DetectionResult& det) {
    // if (!camera_intrinsics_ || !camera_intrinsics_->isInitialized()) {
    //     // LOG_WARNING("相机内参未初始化，跳过相机坐标转换");
    //     return false;
    // }

    // if (det.physical_distance <= 0) {
    //     // LOG_WARNING("物理距离无效，无法进行相机坐标转换");
    //     return false;
    // }

    // 将物理距离从厘米转换为米（如果相机内参使用米作为单位）
    // 这里假设physical_distance是厘米，相机坐标系也使用厘米
    double depth_left = det.left_distance;
    double depth_right = det.right_distance;

    // // 计算bbox中心点的像素坐标
    // int center_x = (det.bbox.x1 + det.bbox.x2) / 2;
    // int center_y = (det.bbox.y1 + det.bbox.y2) / 2;

    // // 转换bbox中心点到相机坐标系
    // auto [cam_x, cam_y, cam_z] = camera_intrinsics_->pixelToCamera(center_x, center_y, depth);
    // det.camera_coords.center = CameraPoint(cam_x, cam_y, cam_z);

    // // 转换bbox四个角点到相机坐标系
    // // 左上角 (x1, y1)
    // auto [cam_x1, cam_y1, cam_z1] = camera_intrinsics_->pixelToCamera(det.bbox.x1, det.bbox.y1, depth);
    // det.camera_coords.top_left = CameraPoint(cam_x1, cam_y1, cam_z1);

    // // 右上角 (x2, y1)
    // auto [cam_x2, cam_y2, cam_z2] = camera_intrinsics_->pixelToCamera(det.bbox.x2, det.bbox.y1, depth);
    // det.camera_coords.top_right = CameraPoint(cam_x2, cam_y2, cam_z2);

    // 右下角 (xmax, ymax)
    auto [cam_x3, cam_y3, cam_z3] = camera_intrinsics_->pixelToCamera(det.bbox_flag.box.xmax, det.bbox_flag.box.ymax, depth_right);
    det.camera_coords.bottom_right = CameraPoint(cam_x3, cam_y3, cam_z3);
    // det.camera_x3 = cam_x3;
    // det.camera_y3 = cam_y3;
    // det.camera_z3 = cam_z3;

    // 左下角 (xmin, ymax)
    auto [cam_x4, cam_y4, cam_z4] = camera_intrinsics_->pixelToCamera(det.bbox_flag.box.xmin, det.bbox_flag.box.ymax, depth_left);
    det.camera_coords.bottom_left = CameraPoint(cam_x4, cam_y4, cam_z4);
    // det.camera_x4 = cam_x4;
    // det.camera_y4 = cam_y4;
    // det.camera_z4 = cam_z4;

    LOG_DEBUG("左下(", det.camera_coords.bottom_left.x, ",", det.camera_coords.bottom_left.y, ",", det.camera_coords.bottom_left.z, "), "
              "右下(", det.camera_coords.bottom_right.x, ",", det.camera_coords.bottom_right.y, ",", det.camera_coords.bottom_right.z, ")");

    return true;
}

DetectionResult DetectionProcessor::processDetectionResult(DetectionResult& det, DetectionType type) {
    // 获取标签名称用于日志
    std::string label_name = "";
    if (app_config_ && det.bbox_flag.box.label >= 0 && det.bbox_flag.box.label < static_cast<int>(app_config_->class_names.size())) {
        label_name = app_config_->class_names[det.bbox_flag.box.label];
    }
    LOG_DEBUG("处理检测结果: 标签ID:", det.bbox_flag.box.label, ", 标签名称:", label_name, ", 置信度:", det.bbox_flag.box.score, ", 测距标志:", det.bbox_flag.flag);

    // 检查是否需要测距
    if (!det.shouldMeasureDistance()) {
        LOG_INFO("测距标志为0，跳过测距处理");
        return det;
    }

    auto [is_reasonable, z1, z2, size_y, des] = isDetectionReasonable(det, type);

    det.length = size_y;
    // det.des = des;
    // 更新检测结果的物理距离
    det.left_distance = z1;
    det.right_distance = z2;
    det.physical_distance = (z1 + z2) / 2;  // 使用左下角和右下角距离的平均值

    // 转换像素坐标到相机坐标系
    if (camera_intrinsics_ && camera_intrinsics_->isInitialized()) {
        if (is_reasonable) {
            convertPixelToCameraCoordinates(det);
            LOG_INFO("已将检测框转换为相机坐标系");
        }
    } else {
        LOG_DEBUG("相机内参未初始化，跳过相机坐标转换");
    }

    return det;
}

DetectionResult DetectionProcessor::processWirePointDetection(DetectionResult& det) {
    // 获取标签名称用于日志
    std::string label_name = "";
    if (app_config_ && det.bbox_flag.box.label >= 0 && det.bbox_flag.box.label < static_cast<int>(app_config_->class_names.size())) {
        label_name = app_config_->class_names[det.bbox_flag.box.label];
    }
    LOG_DEBUG("处理高精度电线检测点: 标签ID:", det.bbox_flag.box.label, ", 标签名称:", label_name, ", 置信度:", det.bbox_flag.box.score, ", 测距标志:", det.bbox_flag.flag);

    // 检查是否需要测距
    if (!det.shouldMeasureDistance()) {
        LOG_INFO("测距标志为0，跳过测距处理");
        return det;
    }

    if (!pixel_converter_) {
        LOG_ERROR("PixelConverter 未初始化");
        return det;
    }

    // 对于高精度电线检测，bbox的四个坐标应该是同一个点
    // 使用中心点坐标进行距离计算
    int center_x = static_cast<int>((det.bbox_flag.box.xmin + det.bbox_flag.box.xmax) / 2);
    int center_y = static_cast<int>((det.bbox_flag.box.ymin + det.bbox_flag.box.ymax) / 2);

    LOG_DEBUG("电线检测点坐标: (", center_x, ", ", center_y, ")");

    // 检查像素坐标是否在有效范围内
    if (!pixel_converter_->isValidPixelCoordinate(center_x, center_y)) {
        // LOG_WARNING("电线检测点超出测距范围");
        return det;
    }

    try {
        // 获取鲁棒的物理坐标
        auto [z1, y1] = getRobustPhysicalCoordinate(center_x, center_y);

        if (!isValidCoordinate(z1, y1)) {
            LOG_WARNING("无法获取有效的物理坐标");
            return det;
        }

        // 计算物理距离（使用点到原点的距离）
        // double distance = std::sqrt(phys_x * phys_x + phys_y * phys_y);
        double distance = z1;
        // LOG_DEBUG("电线检测点物理坐标: (", phys_x, ", ", phys_y, "), 距离: ", distance, " cm");
        if (distance >= pixel_converter_->getXMax()) {
            // LOG_WARNING("电线检测点距离过远");
            return det;
        }
        // 更新检测结果
        det.physical_distance = distance;
        det.left_distance = distance;   // 对于点检测，左右距离相同
        det.right_distance = distance;
        det.length = 0.0;  // 点检测没有长度概念

        // 转换到相机坐标系
        if (camera_intrinsics_ && camera_intrinsics_->isInitialized()) {
            auto [cam_x, cam_y, cam_z] = camera_intrinsics_->pixelToCamera(center_x, center_y, distance);
            det.camera_coords.bottom_left = CameraPoint(cam_x, cam_y, cam_z);
            det.camera_coords.bottom_right = CameraPoint(cam_x, cam_y, cam_z);  // 点检测时左右坐标相同
            LOG_DEBUG("电线检测点相机坐标: (", cam_x, ", ", cam_y, ", ", cam_z, ")");
        }

    } catch (const std::exception& e) {
        LOG_ERROR("处理电线检测点时发生错误:", e.what());
    }

    return det;
}

std::vector<DetectionResult> DetectionProcessor::processDetectionInput(const DetectionInput& input) {
    std::vector<DetectionResult> results;

    LOG_INFO("开始处理检测输入，类型:", (input.type == DetectionType::BOX_DETECTION ? "BOX_DETECTION" : "POINT_DETECTION"),
             ", 检测数量:", input.boxes.size());

    for (const auto& bbox_flag : input.boxes) {
        DetectionResult det(bbox_flag);

        // 根据检测类型选择处理方法
        if (input.type == DetectionType::BOX_DETECTION) {
            // 传统的目标检测框处理
            det = processDetectionResult(det, input.type);
        } else if (input.type == DetectionType::POINT_DETECTION) {
            // 高精度电线检测点处理
            det = processWirePointDetection(det);
        }

        results.push_back(det);
    }

    LOG_INFO("检测处理完成，输出结果数量:", results.size());
    return results;
}

// 高精度电线检测的辅助方法实现

double DetectionProcessor::euclideanDistance(const std::pair<double, double>& p1, const std::pair<double, double>& p2) {
    double dx = p1.first - p2.first;
    double dy = p1.second - p2.second;
    return std::sqrt(dx * dx + dy * dy);
}

std::pair<std::pair<double, double>, std::pair<double, double>> DetectionProcessor::findEndpointsByDistance(
    const std::vector<std::pair<double, double>>& points) {

    if (points.size() < 2) {
        return {{0, 0}, {0, 0}};
    }

    double max_dist = -1;
    std::pair<double, double> endpoint1 = {0, 0};
    std::pair<double, double> endpoint2 = {0, 0};

    for (size_t i = 0; i < points.size(); ++i) {
        for (size_t j = i + 1; j < points.size(); ++j) {
            double d = euclideanDistance(points[i], points[j]);
            if (d > max_dist) {
                max_dist = d;
                endpoint1 = points[i];
                endpoint2 = points[j];
            }
        }
    }

    return {endpoint1, endpoint2};
}

std::vector<int> DetectionProcessor::sortPointsGreedyIndex(const std::vector<std::pair<double, double>>& points) {
    if (points.empty()) {
        return {};
    }

    // 找到两个端点（最远的两点）
    auto [start, _] = findEndpointsByDistance(points);

    // 找到起始点的索引
    int start_idx = 0;
    for (size_t i = 0; i < points.size(); ++i) {
        if (points[i].first == start.first && points[i].second == start.second) {
            start_idx = i;
            break;
        }
    }

    // 初始化
    std::set<int> remaining;
    for (int i = 0; i < static_cast<int>(points.size()); ++i) {
        remaining.insert(i);
    }

    std::vector<int> ordered_indices;
    ordered_indices.push_back(start_idx);
    remaining.erase(start_idx);

    while (!remaining.empty()) {
        int last_idx = ordered_indices.back();
        auto last_point = points[last_idx];

        // 在剩下的点中找与当前点最近的
        int next_idx = -1;
        double min_dist = std::numeric_limits<double>::max();

        for (int i : remaining) {
            double dist = euclideanDistance(last_point, points[i]);
            if (dist < min_dist) {
                min_dist = dist;
                next_idx = i;
            }
        }

        if (next_idx != -1) {
            ordered_indices.push_back(next_idx);
            remaining.erase(next_idx);
        } else {
            break;
        }
    }

    return ordered_indices;
}

std::pair<std::vector<BBox>, std::vector<std::pair<DetectionResult, std::pair<double, double>>>>
DetectionProcessor::groupValidWindowsSingleMerge(
    const std::vector<std::pair<DetectionResult, std::pair<double, double>>>& valid_results,
    int window_size,
    double max_z_diff_threshold,
    double pixel_gap_threshold
) {

    auto is_continuous = [&](const std::vector<std::pair<DetectionResult, std::pair<double, double>>>& points) {
        for (size_t i = 0; i < points.size() - 1; ++i) {
            auto p1 = points[i].second;
            auto p2 = points[i + 1].second;
            if (euclideanDistance(p1, p2) > pixel_gap_threshold) {
                return false;
            }
        }
        return true;
    };

    auto z_diff_within = [&](const std::vector<std::pair<DetectionResult, std::pair<double, double>>>& points) {
        if (points.empty()) return false;
        double first_dist = points[0].first.physical_distance;
        double last_dist = points.back().first.physical_distance;
        double diff = std::abs(last_dist - first_dist);
        bool within = diff <= max_z_diff_threshold;
        LOG_DEBUG("距离差检查: 首点距离=", first_dist, ", 末点距离=", last_dist, ", 差值=", diff, ", 阈值=", max_z_diff_threshold, ", 结果=", within);
        return within;
    };

    auto create_box = [](const std::vector<std::pair<DetectionResult, std::pair<double, double>>>& points) {
        if (points.empty()) return BBox();

        std::vector<double> xs, ys;
        for (const auto& p : points) {
            xs.push_back(p.second.first);
            ys.push_back(p.second.second);
        }

        auto min_x = *std::min_element(xs.begin(), xs.end());
        auto max_x = *std::max_element(xs.begin(), xs.end());
        auto min_y = *std::min_element(ys.begin(), ys.end());
        auto max_y = *std::max_element(ys.begin(), ys.end());

        BBox box;
        box.xmin = static_cast<float>(min_x);
        box.ymin = static_cast<float>(min_y);
        box.xmax = static_cast<float>(max_x);
        box.ymax = static_cast<float>(max_y);
        box.score = points[0].first.bbox_flag.box.score;
        box.label = points[0].first.bbox_flag.box.label;

        return box;
    };

    auto center_of_points = [](const std::vector<std::pair<DetectionResult, std::pair<double, double>>>& points) {
        if (points.empty()) return std::make_pair(0.0, 0.0);

        double sum_x = 0, sum_y = 0;
        for (const auto& p : points) {
            sum_x += p.second.first;
            sum_y += p.second.second;
        }
        return std::make_pair(sum_x / points.size(), sum_y / points.size());
    };

    auto split_window_if_not_nearby = [&](const std::vector<std::pair<DetectionResult, std::pair<double, double>>>& window_points) {
        std::vector<std::vector<std::pair<DetectionResult, std::pair<double, double>>>> sub_windows;
        if (window_points.empty()) return sub_windows;

        std::vector<std::pair<DetectionResult, std::pair<double, double>>> current = {window_points[0]};

        for (size_t i = 1; i < window_points.size(); ++i) {
            auto prev = window_points[i - 1].second;
            auto curr = window_points[i].second;

            if (euclideanDistance(prev, curr) <= pixel_gap_threshold) {
                current.push_back(window_points[i]);
            } else {
                if (current.size() >= 2) {
                    sub_windows.push_back(current);
                }
                current = {window_points[i]};
            }
        }

        if (current.size() >= 2) {
            sub_windows.push_back(current);
        }

        return sub_windows;
    };

    // 定义组结构
    struct Group {
        std::vector<std::pair<DetectionResult, std::pair<double, double>>> points;
        bool valid;
    };

    size_t i = 0;
    std::vector<Group> groups;
    std::vector<std::vector<std::pair<DetectionResult, std::pair<double, double>>>> insufficient_segments;

    // LOG_INFO("开始分组处理，输入点数:", valid_results.size(), ", 窗口大小:", window_size);

    while (i < valid_results.size()) {
        size_t end_idx = std::min(i + window_size, valid_results.size());
        std::vector<std::pair<DetectionResult, std::pair<double, double>>> window(
            valid_results.begin() + i, valid_results.begin() + end_idx);

        if (window.size() < static_cast<size_t>(window_size)) {
            insufficient_segments.push_back(window);
            break;
        }

        if (is_continuous(window)) {
            bool valid_flag = z_diff_within(window);
            // LOG_INFO("窗口连续，大小:", window.size(), ", 有效性:", valid_flag);
            if (window.size() < static_cast<size_t>(window_size)) {
                insufficient_segments.push_back(window);
                LOG_INFO("窗口大小不足，加入不足段");
            } else {
                Group group;
                group.points = window;
                group.valid = valid_flag;
                groups.push_back(group);
                // LOG_INFO("添加组，有效性:", valid_flag);
            }
            i += window_size;
        } else {
            // LOG_INFO("窗口不连续，进行分割");
            auto sub_windows = split_window_if_not_nearby(window);
            // LOG_INFO("分割后得到", sub_windows.size(), "个子窗口");
            for (const auto& sub : sub_windows) {
                // Python版本：window_size=3时，window_size-3=0，所以只有空窗口才被认为不足
                if (sub.size() < static_cast<size_t>(std::max(0, window_size - 3))) {
                    insufficient_segments.push_back(sub);
                    // LOG_INFO("子窗口大小不足:", sub.size());
                } else {
                    bool valid_flag = z_diff_within(sub);
                    Group group;
                    group.points = sub;
                    group.valid = valid_flag;
                    groups.push_back(group);
                    // LOG_INFO("添加子窗口组，大小:", sub.size(), ", 有效性:", valid_flag);
                }
            }
            i += window_size;
        }
    }

    // 处理尾部
    if (i < valid_results.size()) {
        std::vector<std::pair<DetectionResult, std::pair<double, double>>> tail(
            valid_results.begin() + i, valid_results.end());

        auto tail_sub_windows = split_window_if_not_nearby(tail);
        for (const auto& sub : tail_sub_windows) {
            // Python版本：尾部处理时，小于window_size的才被认为不足
            if (sub.size() < static_cast<size_t>(window_size)) {
                insufficient_segments.push_back(sub);
                // LOG_INFO("尾部子窗口大小不足:", sub.size());
            } else {
                Group group;
                group.points = sub;
                group.valid = z_diff_within(sub);
                groups.push_back(group);
                LOG_INFO("添加尾部子窗口组，大小:", sub.size(), ", 有效性:", group.valid);
            }
        }
    }

    // 分离合法和不合法的组
    std::vector<Group> legal_groups;
    std::vector<std::pair<double, double>> legal_centers;

    for (const auto& g : groups) {
        if (g.valid) {
            legal_groups.push_back(g);
            legal_centers.push_back(center_of_points(g.points));
        }
    }

    // LOG_INFO("初始合法组数量:", legal_groups.size());
    // LOG_INFO("总组数量:", groups.size());

    // 将不合法组合并到最近的合法组
    for (const auto& g : groups) {
        if (!g.valid) {
            auto c = center_of_points(g.points);

            if (!legal_centers.empty()) {
                double min_dist = std::numeric_limits<double>::max();
                size_t nearest_idx = 0;

                for (size_t j = 0; j < legal_centers.size(); ++j) {
                    double dist = euclideanDistance(c, legal_centers[j]);
                    if (dist < min_dist) {
                        min_dist = dist;
                        nearest_idx = j;
                    }
                }

                legal_groups[nearest_idx].points.insert(
                    legal_groups[nearest_idx].points.end(),
                    g.points.begin(),
                    g.points.end()
                );
                LOG_INFO("将无效组合并到合法组", nearest_idx, ", 距离:", min_dist);
            }
        }
    }

    // 处理不足的段
    for (const auto& seg : insufficient_segments) {
        auto c = center_of_points(seg);

        if (!legal_centers.empty()) {
            double min_dist = std::numeric_limits<double>::max();
            size_t nearest_idx = 0;

            for (size_t j = 0; j < legal_centers.size(); ++j) {
                double dist = euclideanDistance(c, legal_centers[j]);
                if (dist < min_dist) {
                    min_dist = dist;
                    nearest_idx = j;
                }
            }

            legal_groups[nearest_idx].points.insert(
                legal_groups[nearest_idx].points.end(),
                seg.begin(),
                seg.end()
            );
        }
    }

    // 构建最终框和过滤结果（只包含合法组）
    std::vector<BBox> boxes;
    std::vector<std::pair<DetectionResult, std::pair<double, double>>> filtered_valid_results;

    LOG_INFO("最终合法组数量:", legal_groups.size());
    for (const auto& g : legal_groups) {
        BBox box = create_box(g.points);
        boxes.push_back(box);
        LOG_INFO("创建框，包含点数:", g.points.size());

        filtered_valid_results.insert(filtered_valid_results.end(), g.points.begin(), g.points.end());
    }

    return {boxes, filtered_valid_results};
}

std::vector<DetectionResult> DetectionProcessor::convertBoxesToDetectionResults(const std::vector<BBox>& boxes) {
    std::vector<DetectionResult> detection_results;

    for (size_t i = 0; i < boxes.size(); ++i) {
        const auto& box = boxes[i];
        BBoxFlag bbox_flag;
        bbox_flag.box = box;
        bbox_flag.flag = 1.0f;  // 默认需要测距

        DetectionResult det_result(bbox_flag);
        detection_results.push_back(det_result);
    }

    return detection_results;
}

std::vector<DetectionResult> DetectionProcessor::processWireDetectionWithGrouping(
    const DetectionInput& input,
    int window_size,
    double max_z_diff_threshold,
    double pixel_gap_threshold
) {
    LOG_INFO("开始处理高精度电线检测，包含分组逻辑");
    window_size = app_config_->window_size;
    max_z_diff_threshold = app_config_->max_z_diff_threshold;
    pixel_gap_threshold = app_config_->pixel_gap_threshold;
    if (input.type != DetectionType::POINT_DETECTION) {
        LOG_WARNING("输入类型不是点检测，使用普通处理方法");
        return processDetectionInput(input);
    }

    if (input.boxes.size() <= 1) {
        LOG_INFO("检测点数量不足，使用普通处理方法");
        return processDetectionInput(input);
    }

    // 第一步：处理所有点检测，获取物理距离
    std::vector<std::pair<DetectionResult, std::pair<double, double>>> valid_results;

    for (const auto& bbox_flag : input.boxes) {
        DetectionResult det(bbox_flag);
        // LOG_INFO("处理电线检测点: (", bbox_flag.box.xmin, ",", bbox_flag.box.ymin, "), flag:", bbox_flag.flag);
        det = processWirePointDetection(det);

        // LOG_INFO("处理后距离:", det.physical_distance);
        if (det.physical_distance > 0) {
            // 计算点坐标（bbox的中心点）
            double center_x = (bbox_flag.box.xmin + bbox_flag.box.xmax) / 2.0;
            double center_y = (bbox_flag.box.ymin + bbox_flag.box.ymax) / 2.0;
            valid_results.push_back({det, {center_x, center_y}});
            // LOG_INFO("添加有效点: (", center_x, ",", center_y, "), 距离:", det.physical_distance);
        }
    }

    LOG_INFO("有效检测点数量:", valid_results.size());

    if (valid_results.size() <= 1) {
        LOG_INFO("有效检测点数量不足，返回原始结果");
        std::vector<DetectionResult> results;
        for (const auto& pair : valid_results) {
            results.push_back(pair.first);
        }
        return results;
    }

    // 第二步：点排序
    std::vector<std::pair<double, double>> points;
    for (const auto& pair : valid_results) {
        points.push_back(pair.second);
    }

    std::vector<int> sorted_indices = sortPointsGreedyIndex(points);

    // 重新排序valid_results
    std::vector<std::pair<DetectionResult, std::pair<double, double>>> sorted_valid_results;
    for (int idx : sorted_indices) {
        sorted_valid_results.push_back(valid_results[idx]);
    }

    // 第三步：窗口分组
    auto [boxes, filtered_results] = groupValidWindowsSingleMerge(
        sorted_valid_results,
        window_size,
        max_z_diff_threshold,
        pixel_gap_threshold
    );

    LOG_INFO("分组完成，生成", boxes.size(), "个框");

    // 第四步：将框转换为检测结果并计算物理距离
    std::vector<DetectionResult> box_detection_results = convertBoxesToDetectionResults(boxes);
    std::vector<DetectionResult> final_results;

    // 1. 先把 sorted_valid_results 的 DetectionResult 部分加入 final_results
    for (const auto& valid_pair : sorted_valid_results) {
        const DetectionResult& det_result = valid_pair.first;
        final_results.push_back(det_result);
    }

    for (auto& box_det_result : box_detection_results) {
        // 对框进行物理距离计算
        box_det_result = processDetectionResult(box_det_result, DetectionType::POINT_DETECTION);
        if (box_det_result.physical_distance > 0) {
            final_results.push_back(box_det_result);
        }
    }

    LOG_INFO("高精度电线检测处理完成，最终输出", final_results.size(), "个结果");
    return final_results;
}

