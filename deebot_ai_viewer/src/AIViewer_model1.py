# -*- coding: utf-8 -*-
"""
DrawMap QT5
Create by Chengqi.Lv
2019.11.4
"""

from PyQt5 import QtCore, QtGui, QtWidgets
import numpy as np
import cv2
import copy
import os
import socket
import threading
import sys
import json
import datetime
import inspect
import ctypes
import glob

CFG_LOCAL_IP = '0.0.0.0'
CFG_LOCAL_PORT = 8888
PKT_BUFF_SIZE = 10485760
MAIN_FILE_PATH = os.path.dirname(os.path.abspath(__file__))
print(MAIN_FILE_PATH)
upscale = 0.25

# label_map = {0: "Trash can", 1: "Charging dock", 2: "Cleaning cloth" , 3: "Rug", 4:  "Shoes", 5: "Wire", 6: "Sliding rail", 7: "Wheels"}

label_map = {0: "trash_can", 
             1: "cloth", 
             2: "rug" , 
             3: "shoes", 
             4:  "wire", 
             5: "rail", 
             6: "wheels", 
             7: "seat_base", 
             8: "scales",
             9: "sandbasin",
             10: "bei_bowl",
             11: "wan_bowl",
             12: "big_shack",
             13: "sml_shack",
             14: "shit"
             }

colors = [
    (255, 0, 0),      # 红色 - class 1
    (0, 255, 0),      # 绿色 - class 2
    (0, 0, 255),      # 蓝色 - class 3
    (255, 255, 0),    # 青色 - class 4
    (255, 0, 255),    # 紫色 - class 5
    (0, 255, 255),    # 黄色 - class 6
    (255, 128, 0),    # 橙色 - class 7
    (128, 0, 255),    # 紫红色 - class 8
    (0, 255, 128),    # 春绿色 - class 9
    (128, 128, 0),    # 橄榄绿 - class 10
    (0, 128, 128),    # 蓝绿色 - class 11
    (128, 0, 128),    # 紫色 - class 12
    (192, 192, 192),  # 银灰色 - class 13
    (255, 165, 0),    # 橙黄色 - class 14
    (0, 128, 255),    # 天蓝色 - class 15
]

color_map = {i: color for i, color in enumerate(colors, start=0)}


def read_map(filepath):
    f = open(filepath)
    map = np.fromfile(f, dtype=np.float32)
    map = np.reshape(map, (720, 1280))
    return map


def get_time_string(style='show'):
    if style == 'show':
        return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    elif style == 'save':
        return datetime.datetime.now().strftime('%Y_%m_%d_%H_%M_%S_%f')
    else:
        raise ValueError


def add_coco_box(img, resultJson):
    for ret in resultJson:
        xmin = ret['xmin'] * upscale
        ymin = ret['ymin'] * upscale
        xmax = ret['xmax'] * upscale
        ymax = ret['ymax'] * upscale

        x_min = int(xmin)
        x_max = int(xmax)
        y_min = int(ymin)
        y_max = int(ymax)

        picwidth = img.shape[1]
        picheight = img.shape[0]

        if x_min < 0 or x_min > picwidth:
            x_min = 0
        if x_max < 0 or x_max > picwidth:
            x_max = 0

        if y_min < 0 or y_min > picheight:
            y_min = 0
        if y_max < 0 or y_max > picheight:
            y_max = 0

        if x_max >= picwidth:
            x_max = picwidth - 1
        if y_max >= picheight:
            y_max = picheight - 1

        if x_min < 0:
            x_min = 0
        if y_min < 0:
            y_min = 0
        clr = color_map.get(ret['class'], (255, 255, 255))
        cv2.rectangle(img, (x_min, y_min), (x_max, y_max), clr, 1)
        # show txt
        txt = '{}:{:.3f}'.format(label_map.get(ret['class'], 'unknown'), ret['score'])
        if 'physical_distance' in ret and ret['physical_distance'] > 0:
            txt += '  d:{:.3f}'.format(ret['physical_distance'])
        font = cv2.FONT_HERSHEY_SIMPLEX
        cat_size = cv2.getTextSize(txt, font, 0.5, 2)[0]
        cv2.rectangle(img, (x_min, y_min + cat_size[1] + 1), (x_min + cat_size[0] + cat_size[1], y_min + 1), clr, -1)
        cv2.putText(img, txt, (x_min, y_min + cat_size[1]), font, 0.4, (0, 0, 0), thickness=1, lineType=cv2.LINE_AA)
        
    return img


def _async_raise(tid, exctype):
    """raises the exception, performs cleanup if needed"""
    print('killing thread, id=', tid)
    tid = ctypes.c_long(tid)
    if not inspect.isclass(exctype):
        exctype = type(exctype)
    res = ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, ctypes.py_object(exctype))
    if res == 0:
        raise ValueError("invalid thread id")
    elif res != 1:
        # """if it returns a number greater than one, you're in trouble,
        # and you should call it again with exc=NULL to revert the effect"""
        ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, None)
        raise SystemError("PyThreadState_SetAsyncExc failed")


def stop_thread(thread):
    _async_raise(thread.ident, SystemExit)

class Folder_Selector():
    def __init__(self):
        self.imlist = []

    def setup(self, file_path):
        self.cur_name = file_path
        folder_path = os.path.dirname(file_path)
        if os.path.isdir(folder_path):
            self.imlist = glob.glob(os.path.join(folder_path, '*.jpg'))
            self.imlist.sort()

    def get_next(self):
        cur_id = self.imlist.index(self.cur_name)
        if cur_id < len(self.imlist)-1:
            self.cur_name = self.imlist[cur_id + 1]
            return self.cur_name
        else:
            # self.cur_name = self.imlist[0]
            return None


    def get_pre(self):
        cur_id = self.imlist.index(self.cur_name)
        if cur_id > 0:
            self.cur_name = self.imlist[cur_id - 1]
            return self.cur_name
        else:
            # self.cur_name = self.imlist[-1]
            return None


class Ui_MainWindow(object):
    def __init__(self):
        self.main_img = cv2.imread(os.path.join(MAIN_FILE_PATH, './resource/no_signal.jpg')) if \
            os.path.isfile(os.path.join(MAIN_FILE_PATH, './resource/no_signal.jpg')) else \
            np.zeros((360, 720, 3), dtype=np.uint8)
        self.show_img = copy.deepcopy(self.main_img)
        self.mapxy_path = './'
        self.mapx = None
        self.mapy = None
        self.server_thread = None
        self.result_json = None
        self.data = b''
        self.show_text = 'Welcome!'
        self.istosave = False
        self.folderSelector = Folder_Selector()

    def setupUi(self, MainWindow):
        """
        控件定义函数
        :param MainWindow:
        :return:
        """
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(861, 596)
        MainWindow.setMinimumSize(QtCore.QSize(830, 540))
        # MainWindow.setWindowIcon(QtGui.QIcon('./resource/no_signal.jpg'))

        self.showTextSignal = MainWindow.showTextSignal

        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.buttonGroup = QtWidgets.QButtonGroup(self.centralwidget)
        self.buttonGroup.setExclusive(False)

        self.centralwidget2 = QtWidgets.QWidget(MainWindow)
        self.centralwidget2.setObjectName("centralwidget2")
        self.buttonGroup2 = QtWidgets.QButtonGroup(self.centralwidget2)
        self.buttonGroup2.setExclusive(False)

        self.gridLayout = QtWidgets.QGridLayout(self.centralwidget)
        self.gridLayout.setObjectName("gridLayout")
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setObjectName("verticalLayout")

        self.pushButton_Connect = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_Connect.setObjectName("pushButton_Connect")
        self.verticalLayout.addWidget(self.pushButton_Connect)

        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem)

        self.pushButton_Disconnect = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_Disconnect.setEnabled(False)
        self.pushButton_Disconnect.setObjectName("pushButton_Disconnect")
        self.verticalLayout.addWidget(self.pushButton_Disconnect)

        spacerItem1 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem1)

        self.pushButton_startsave = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_startsave.setEnabled(False)
        self.pushButton_startsave.setObjectName("pushButton_startsave")
        self.verticalLayout.addWidget(self.pushButton_startsave)

        spacerItem2 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem2)

        self.pushButton_stopsave = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_stopsave.setEnabled(False)
        self.pushButton_stopsave.setObjectName("pushButton_stopsave")
        self.verticalLayout.addWidget(self.pushButton_stopsave)

        spacerItem3 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem3)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")

        self.pushButton_Remap = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_Remap.setEnabled(False)
        self.pushButton_Remap.setObjectName("pushButton_Remap")
        self.horizontalLayout.addWidget(self.pushButton_Remap)

        self.radioButtonRemap = QtWidgets.QRadioButton(self.centralwidget)
        self.buttonGroup.addButton(self.radioButtonRemap)
        self.radioButtonRemap.setEnabled(False)
        self.radioButtonRemap.setText("")
        self.radioButtonRemap.setCheckable(True)
        self.radioButtonRemap.setChecked(False)
        self.radioButtonRemap.setObjectName("radioButtonRemap")
        self.horizontalLayout.addWidget(self.radioButtonRemap)
        self.verticalLayout.addLayout(self.horizontalLayout)
        spacerItem4 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem4)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.pushButton_Rotate = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_Rotate.setObjectName("pushButton_Rotate")
        self.horizontalLayout_2.addWidget(self.pushButton_Rotate)

        self.radioButtonRotate = QtWidgets.QRadioButton(self.centralwidget2)
        self.buttonGroup2.addButton(self.radioButtonRotate)
        self.radioButtonRotate.setEnabled(False)
        self.radioButtonRotate.setText("")
        self.radioButtonRotate.setObjectName("radioButtonRotate")
        self.horizontalLayout_2.addWidget(self.radioButtonRotate)
        self.verticalLayout.addLayout(self.horizontalLayout_2)
        spacerItem5 = QtWidgets.QSpacerItem(10, 1, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem5)
        spacerItem6 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem6)
        spacerItem7 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem7)
        spacerItem8 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem8)
        self.pushButton_Quit = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_Quit.setObjectName("pushButton_Quit")
        self.verticalLayout.addWidget(self.pushButton_Quit)
        self.gridLayout.addLayout(self.verticalLayout, 0, 0, 1, 1)
        self.verticalLayout_2 = QtWidgets.QVBoxLayout()
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.frame = QtWidgets.QFrame(self.centralwidget)
        self.frame.setMinimumSize(QtCore.QSize(640, 320))
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.gridLayout_2 = QtWidgets.QGridLayout(self.frame)
        self.gridLayout_2.setObjectName("gridLayout_2")

        # Qlabel
        self.label_Img = QtWidgets.QLabel(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_Img.sizePolicy().hasHeightForWidth())
        self.label_Img.setSizePolicy(sizePolicy)
        self.label_Img.setText("")
        self.label_Img.setObjectName("label_Img")
        self.gridLayout_2.addWidget(self.label_Img, 0, 0, 1, 1)
        # show img
        height, width, channel = self.main_img.shape
        bytesPerline = 3 * width
        self.qimg = QtGui.QImage(self.main_img.data, width, height, bytesPerline,
                                 QtGui.QImage.Format_RGB888).rgbSwapped()
        self.label_Img.setPixmap(QtGui.QPixmap.fromImage(self.qimg))
        self.label_Img.setAlignment(QtCore.Qt.AlignCenter)  # 设置图像居中

        self.verticalLayout_2.addWidget(self.frame)
        self.textBrowser = QtWidgets.QTextBrowser(self.frame)
        self.textBrowser.setMaximumSize(QtCore.QSize(16777215, 100))
        self.textBrowser.setObjectName("textBrowser")

        self.verticalLayout_2.addWidget(self.textBrowser)
        self.textBrowser.setText(self.show_text)

        self.gridLayout.addLayout(self.verticalLayout_2, 0, 1, 1, 1)

        # 左右按钮
        # lrButtomPolicy = QtWidgets.QSizePolicy.Fixed
        self.horizontalLayout_lr = QtWidgets.QHBoxLayout(self.label_Img)
        self.horizontalLayout_lr.setObjectName("horizontalLayout_lr")
        spacerItem9 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.pushButton_Left = QtWidgets.QPushButton(self.label_Img)
        self.pushButton_Left.setObjectName("pushButton_Left")
        self.pushButton_Left.setFixedSize(30, 30)  # 设为固定大小
        self.horizontalLayout_lr.addWidget(self.pushButton_Left)
        self.horizontalLayout_lr.addItem(spacerItem9)
        self.pushButton_Right = QtWidgets.QPushButton(self.label_Img)
        self.pushButton_Right.setObjectName("pushButton_right")
        self.pushButton_Right.setFixedSize(30, 30)
        self.horizontalLayout_lr.addWidget(self.pushButton_Right)

        self.pushButton_Left.setVisible(False)
        self.pushButton_Right.setVisible(False)


        MainWindow.setCentralWidget(self.centralwidget)
        self.menubar = QtWidgets.QMenuBar(MainWindow)
        self.menubar.setGeometry(QtCore.QRect(0, 0, 861, 28))
        self.menubar.setObjectName("menubar")
        self.menubar.setNativeMenuBar(False)  # 不使用系统自带的菜单
        self.menuFile = QtWidgets.QMenu(self.menubar)
        self.menuFile.setObjectName("menuFile")
        self.menuSettings = QtWidgets.QMenu(self.menubar)
        self.menuSettings.setObjectName("menuSettings")
        MainWindow.setMenuBar(self.menubar)
        self.statusbar = QtWidgets.QStatusBar(MainWindow)
        self.statusbar.setObjectName("statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.actionOpen = QtWidgets.QAction(MainWindow)
        self.actionOpen.setObjectName("actionOpen")

        self.actionSave = QtWidgets.QAction(MainWindow)
        self.actionSave.setObjectName("actionSave")

        self.actionLoadMap = QtWidgets.QAction(MainWindow)
        self.actionLoadMap.setObjectName("actionLoadMap")

        self.actionSave_format = QtWidgets.QAction(MainWindow)
        self.actionSave_format.setObjectName("actionSave_format")

        self.menuFile.addAction(self.actionOpen)
        self.menuFile.addAction(self.actionSave)
        self.menuFile.addAction(self.actionLoadMap)

        self.menuSettings.addAction(self.actionSave_format)

        self.menubar.addAction(self.menuFile.menuAction())
        self.menubar.addAction(self.menuSettings.menuAction())

        self.retranslateUi(MainWindow)

        #  信号槽连接
        self.pushButton_Connect.clicked.connect(self.connect_click)
        self.pushButton_Disconnect.clicked.connect(self.disconnect_click)
        self.pushButton_Remap.clicked.connect(self.remap_click)
        self.pushButton_Rotate.clicked.connect(self.rotate_click)
        self.pushButton_startsave.clicked.connect(self.start_save_click)
        self.pushButton_stopsave.clicked.connect(self.stop_save_click)
        self.pushButton_Quit.clicked.connect(self.quit_click)
        self.pushButton_Right.clicked.connect(self.next_click)
        self.pushButton_Left.clicked.connect(self.pre_click)

        self.actionOpen.triggered.connect(self.loadimg_click)
        self.actionLoadMap.triggered.connect(self.loadmapxy_click)
        self.showTextSignal.connect(self.text_emit)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "DEEBOT AI Viewer"))
        self.pushButton_Connect.setText(_translate("MainWindow", "Connect"))
        self.pushButton_Disconnect.setText(_translate("MainWindow", "Disconnect"))
        self.pushButton_startsave.setText(_translate("MainWindow", "Start Save"))
        self.pushButton_stopsave.setText(_translate("MainWindow", "Stop Save"))
        self.pushButton_Remap.setText(_translate("MainWindow", "Remap"))
        self.pushButton_Rotate.setText(_translate("MainWindow", "Rotate"))
        self.pushButton_Quit.setText(_translate("MainWindow", "Quit"))
        self.menuFile.setTitle(_translate("MainWindow", "&File"))
        self.menuSettings.setTitle(_translate("MainWindow", "&Settings"))
        self.actionOpen.setText(_translate("MainWindow", "&Open..."))
        self.actionSave.setText(_translate("MainWindow", "&Save..."))
        self.actionSave_format.setText(_translate("MainWindow", "&Save format ..."))
        self.actionLoadMap.setText(_translate("MainWindow", "&Load Mapxy ..."))
        self.pushButton_Left.setText(_translate("MainWindow", "<<"))
        self.pushButton_Right.setText(_translate("MainWindow", ">>"))

    def connect_click(self):
        """
        点击connect，启动socket服务器
        启动后台进程运行服务器，与UI互不影响
        :return:
        """
        self.data = b''  # 每次重连接时清空之前接收到的数据
        self.showTextSignal.emit('Starting listening service on ' + CFG_LOCAL_IP + ':' + str(CFG_LOCAL_PORT) + ' ...')
        self.server_thread = threading.Thread(target=self.tcp_server, args=(CFG_LOCAL_IP, CFG_LOCAL_PORT))
        self.server_thread.start()
        self.pushButton_Disconnect.setEnabled(True)
        self.pushButton_Connect.setEnabled(False)
        self.pushButton_startsave.setEnabled(True)
        self.actionOpen.setEnabled(False)

        self.pushButton_Left.setVisible(False)
        self.pushButton_Right.setVisible(False)

    def disconnect_click(self):
        """
        关闭socket服务器，shutdown用于彻底关闭嵌套字，否则端口仍然会被占用
        嵌套字关闭后，后台服务器进程自动结束
        :return:
        """
        self.local_server.shutdown(2)  # 关闭发送和接受通道
        self.local_server.close()
        print('Stop service by button')
        # stop_thread(self.server_thread)
        self.showTextSignal.emit('Stop TCP Server')

        self.pushButton_Connect.setEnabled(True)
        self.pushButton_Disconnect.setEnabled(False)
        self.pushButton_startsave.setEnabled(False)
        self.pushButton_stopsave.click()
        self.istosave = False
        self.pushButton_stopsave.setEnabled(False)
        self.actionOpen.setEnabled(True)

    def remap_click(self):
        if self.radioButtonRemap.isChecked():
            self.radioButtonRemap.setChecked(False)
        else:
            self.radioButtonRemap.setChecked(True)
        self.refresh_img(result_json=self.result_json)

    def rotate_click(self):
        if self.radioButtonRotate.isChecked():
            self.radioButtonRotate.setChecked(False)
        else:
            self.radioButtonRotate.setChecked(True)
        self.refresh_img(result_json=self.result_json)

    def loadimg_click(self):
        options = QtWidgets.QFileDialog.Options()
        options |= QtWidgets.QFileDialog.DontUseNativeDialog
        file_name, _ = QtWidgets.QFileDialog.getOpenFileName(None, "Open file", "",
                                                             "JPG Files (*.jpg);;All Files (*)",
                                                             options=options)
        self.result_json = None
        json_name = file_name.split('.jpg')[0] + '.json'
        if os.path.isfile(json_name):
            with open(json_name) as f:
                self.result_json = json.load(f)

        if file_name:
            try:
                self.main_img = cv2.imread(file_name)
                self.showTextSignal.emit('Open File: ' + file_name)
                self.refresh_img(result_json=self.result_json, get_new_img=True)
                self.radioButtonRotate.setChecked(False)
                self.radioButtonRemap.setChecked(False)
                # 显示翻页按钮
                self.pushButton_Left.setVisible(True)
                self.pushButton_Right.setVisible(True)
                self.folderSelector = Folder_Selector()  # 每次新建一个
                self.folderSelector.setup(file_name)
            except:
                self.showTextSignal.emit('File Error!')

    def next_click(self):
        file_name = self.folderSelector.get_next()
        if file_name is not None:
            self.result_json = None
            json_name = file_name.split('.jpg')[0] + '.json'
            if os.path.isfile(json_name):
                with open(json_name) as f:
                    self.result_json = json.load(f)
            self.main_img = cv2.imread(file_name)
            self.showTextSignal.emit('Open File: ' + file_name)
            self.refresh_img(result_json=self.result_json, get_new_img=True)
            self.pushButton_Left.setVisible(True)
        else:
            self.pushButton_Right.setVisible(False)
        # print()

    def pre_click(self):
        file_name = self.folderSelector.get_pre()
        if file_name is not None:
            self.result_json = None
            json_name = file_name.split('.jpg')[0] + '.json'
            if os.path.isfile(json_name):
                with open(json_name) as f:
                    self.result_json = json.load(f)
            self.main_img = cv2.imread(file_name)
            self.showTextSignal.emit('Open File: ' + file_name)
            self.refresh_img(result_json=self.result_json, get_new_img=True)
            self.pushButton_Right.setVisible(True)
        else:
            self.pushButton_Left.setVisible(False)

    def loadmapxy_click(self):
        options = QtWidgets.QFileDialog.Options()
        options |= QtWidgets.QFileDialog.DontUseNativeDialog
        folder = QtWidgets.QFileDialog.getExistingDirectory(None, "Select Folder", "", options=options)
        mapx_path = os.path.join(folder, 'mapx')
        mapy_path = os.path.join(folder, 'mapy')
        if os.path.isfile(mapx_path) and os.path.isfile(mapy_path):
            self.mapx = read_map(mapx_path)
            self.mapy = read_map(mapy_path)
        else:
            self.mapx = None
            self.mapy = None
        if self.mapx is not None and self.mapy is not None:
            self.pushButton_Remap.setEnabled(True)
        else:
            self.pushButton_Remap.setEnabled(False)

    def start_save_click(self):
        timestr = get_time_string(style='save')
        self.saveimageDir = os.path.join(MAIN_FILE_PATH, "../images/" + timestr)
        try:
            os.stat(os.path.join(MAIN_FILE_PATH, "../images"))
        except:
            os.mkdir(os.path.join(MAIN_FILE_PATH, "../images"))
        try:
            os.stat(self.saveimageDir)
        except:
            os.mkdir(self.saveimageDir)
        self.istosave = True
        self.pushButton_startsave.setEnabled(False)
        self.pushButton_stopsave.setEnabled(True)

    def stop_save_click(self):
        self.istosave = False
        self.pushButton_startsave.setEnabled(True)
        self.pushButton_stopsave.setEnabled(False)

    def quit_click(self):
        if self.pushButton_Disconnect.isEnabled():
            self.disconnect_click()
        QtCore.QCoreApplication.quit()

    def refresh_img(self, result_json=None, get_new_img=False):
        """
        图像刷新函数，包括了存图、畸变校正、旋转以及画框，
        能够根据窗口大小自适应缩放显示的图片
        :param result_json: 识别结果
        :param get_new_img: 是否是接收图片状态，是则能够开始存图
        :return:
        """
        # ---remap---
        temp_img = copy.deepcopy(self.main_img)  # 必须是深拷贝，否则是原图的引用
        if self.radioButtonRotate.isChecked():
            temp_img = np.array(np.rot90(temp_img, 2), dtype=np.uint8)  # 必须重新建立一个np array，否则无法操作
        if self.radioButtonRemap.isChecked() and self.mapx is not None and self.mapy is not None:
            temp_img = cv2.remap(temp_img, self.mapx, self.mapy, cv2.INTER_LINEAR)

        # if result_json is not None:
        #     model_id = result_json['model_id']
        #     if model_id != 14:
        #         return
        # ---存结果---
        if self.istosave and get_new_img:
            cur_time = get_time_string(style='save')
            img_name = cur_time + '.jpg'
            cv2.imwrite(os.path.join(self.saveimageDir, img_name), temp_img)
            json_name = cur_time + '.json'
            if result_json is not None:
                with open(os.path.join(self.saveimageDir, json_name), 'w') as outfile:
                    json.dump(result_json, outfile)

        # ---画框---
        if result_json is not None:
            model_id = result_json['model_id']
            # 选择显示对应的模型
            # if model_id == 0 or model_id ==7:

            font = cv2.FONT_HERSHEY_COMPLEX
            temp_img = cv2.putText(temp_img, "model:" + str(model_id), (50, 50), font, 1, (0, 255, 0), thickness=1, lineType=cv2.LINE_AA)


            if get_new_img:  # 得到新图时才输出结果
                self.showTextSignal.emit(str(result_json))
            try:
                ret = result_json['ret']
            except:
                ret = None
            
            if ret is not None:  # 防止空结果输入
                temp_img = add_coco_box(temp_img, ret)

        # ---自适应缩放---
        origin_height, origin_width, _ = temp_img.shape
        label_height = self.label_Img.size().height() if self.label_Img.size().height() > 300 else 420
        label_width = self.label_Img.size().width() if self.label_Img.size().width() > 600 else 720
        if origin_height / origin_width > label_height / label_width:
            resize_height = label_height
            resize_width = int(origin_width / origin_height * label_height)
        else:
            resize_width = label_width
            resize_height = int(origin_height / origin_width * label_width)
        self.show_img = cv2.resize(temp_img, (resize_width, resize_height))

        # ---控件显示---
        height, width, channel = self.show_img.shape
        bytes_perline = 3 * width
        self.qimg = QtGui.QImage(self.show_img.data, width, height, bytes_perline,
                                 QtGui.QImage.Format_RGB888).rgbSwapped()
        self.label_Img.setPixmap(QtGui.QPixmap.fromImage(self.qimg))

    def text_emit(self, string):
        self.textBrowser.append(string)

    def tcp_data_receiver(self, conn_receiver):
        while True:
            try:
                data = conn_receiver.recv(PKT_BUFF_SIZE)
            except Exception:
                print('Connection closed.')
                break
            if not data:
                print('No more data is received.')
                break
            if self.pushButton_Connect.isEnabled():
                print('Receiver break out')
                break
            print('receive > %s > %d bytes.' % (conn_receiver.getpeername(), len(data)))
            self.handleData(data)
        conn_receiver.close()
        return

    def tcp_server(self, local_ip, local_port):
        """
        TCP服务器启动及接收
        :param local_ip:
        :param local_port:
        :return:
        """
        self.local_server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.local_server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR,
                                     1)  # 不添加该行，在服务退出后，端口还会处于被占用的状态一段时间，添加上，进程退出，端口也会直接被释放掉
        try:
            self.local_server.bind((local_ip, local_port))
        except:
            self.showTextSignal.emit('8888端口已处于连接状态，若无法使用，请清除端口占用。')
            pass
        self.local_server.listen(5)
        print('Starting listening service on ' + local_ip + ':' + str(local_port) + ' ...')
        while True:
            try:
                (local_conn, local_addr) = self.local_server.accept()
            except Exception:
                try:  # 多种关闭连接的情况
                    self.local_server.shutdown(2)
                    self.local_server.close()
                    print('Stop service by server')
                except:
                    print('Service already close')
                break
            print('start receiver thread')
            # self.receiver_thread = threading.Thread(target=self.tcp_data_receiver, args=(local_conn,))  # 无需再启动一个进程
            # self.receiver_thread.start()
            self.tcp_data_receiver(local_conn)
            print('Receive request from %s:%d.' % local_addr)
        return

    def handleData(self, data):
        """
        数据解析函数
        :param data: socket接受的数据
        :return:
        """
        self.data += data
        # 寻找标志位
        picstart = self.data.find(b'picstart')
        picend = self.data.find(b'picend')
        result_start = self.data.find(b'resultstart')
        result_end = self.data.find(b'resultsend')
        print("picstart:%d picend:%d result_start:%d result_end:%d\n", picstart, picend, result_start, result_end)
        if picstart != -1 and picend != -1 and result_start != -1 and result_end != -1:
            seperate_data = self.data.split(b'picstart')[1]
            seperate_data = seperate_data.split(b'picend')
            print("pic size:%d %d %d %d\n", len(seperate_data[0]), picstart, picend, len(seperate_data))
            # print("len(seperate_data):", len(seperate_data))
            result_content = self.data.split(b'resultstart')[1].split(b'resultsend')[0]
            # print("result :%s\n", result_content.decode('utf-8'))
            self.result_json = None
            if len(result_content) > 1:
                # self.result_json = json.loads(result_content.decode('utf-8')[1:])  # 读取AI识别结果
                try:
                    self.result_json = json.loads(result_content.decode('utf-8',errors='strict').strip())  # 读取AI识别结果
                except:
                    return
            # picdata = seperate_data[0][1:]  # 发过来的图在picstart标志位之后还有一个字节的字符。。。太坑了
            picdata = seperate_data[0]  # 发过来的图在picstart标志位之后还有一个字节的字符。。。太坑了

            picdata = np.asarray(bytearray(picdata), dtype="uint8")
            receive_img = None
            # print("picdata.size:", picdata.size)
            # 检查数据头部是否符合JPEG格式（JPEG文件头以FF D8 FF开头）
            # print("Header:", picdata[:8].tobytes().hex())  # 应输出 'ffd8ffe0' 或类似
            # print("Data type:", picdata.dtype)  # 应输出 uint8
            if picdata.size > 0:  # 防止空图像输入
                receive_img = cv2.imdecode(picdata, cv2.IMREAD_COLOR)  # 解码二进制图片流
            if receive_img is not None:
                self.showTextSignal.emit(get_time_string(style='show') + ': Receive image.')
                self.main_img = receive_img
                self.refresh_img(result_json=self.result_json, get_new_img=True)  # 刷新图片
            if len(self.data.split(b'resultstart')[1].split(b'resultsend')) > 1:
                self.data = self.data.split(b'resultstart')[1].split(b'resultsend')[1]
            elif picend < picstart:
                self.data = self.data[picstart:]
            else:
                self.data = b''
        return


class MainWindow(QtWidgets.QMainWindow):
    resized = QtCore.pyqtSignal()  # 窗口缩放信号
    showTextSignal = QtCore.pyqtSignal(str)  # 文字修改信号

    def __init__(self, parent=None):
        super(MainWindow, self).__init__(parent=parent)
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        self.resized.connect(self.resize_func)

    def resizeEvent(self, a0: QtGui.QResizeEvent) -> None:
        """
        重载resize事件
        :param a0:
        :return:
        """
        self.resized.emit()
        return super(MainWindow, self).resizeEvent(a0)

    def resize_func(self):
        self.ui.refresh_img(result_json=self.ui.result_json)

    def closeEvent(self, a0: QtGui.QCloseEvent) -> None:
        """
        重载close事件
        :param a0:
        :return:
        """
        if self.ui.pushButton_Disconnect.isEnabled():
            self.ui.disconnect_click()


if __name__ == "__main__":
    import sys

    app = QtWidgets.QApplication(sys.argv)
    mainWindow = MainWindow()
    mainWindow.show()
    sys.exit(app.exec_())
