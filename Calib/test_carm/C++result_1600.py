# import cv2
# import numpy as np

# # 创建一个白色背景的图像 (720高 x 1280宽)
# image = np.ones((720, 1280, 3), dtype=np.uint8) * 255

# # 定义点的列表
# points = [
#     (582, 2), (908, 4), (584, 12), (934, 76), (1052, 116),
#     (1064, 114), (1074, 110), (1036, 132), (1046, 126), (1062, 126),
#     (1018, 144), (1030, 142), (1048, 144), (1060, 140), (1006, 160),
#     (1016, 158), (1030, 158), (1048, 158), (1058, 156), (1006, 172),
#     (1016, 174), (1032, 174), (662, 222), (668, 236), (672, 242),
#     (712, 238), (678, 256), (684, 268), (690, 274), (694, 286),
#     (698, 300), (708, 304), (714, 316), (722, 322), (728, 334),
#     (740, 338), (732, 346), (744, 350), (756, 354), (750, 360),
#     (758, 366), (806, 364), (818, 372), (764, 378), (772, 384),
#     (824, 382), (780, 394), (788, 402), (824, 400), (794, 410),
#     (804, 418), (812, 426), (820, 434), (828, 442), (838, 448),
#     (846, 456), (854, 462), (864, 470), (870, 478), (880, 486),
#     (888, 492), (898, 500), (906, 506), (916, 514), (924, 522),
#     (934, 528), (946, 532), (952, 540), (964, 548), (972, 554),
#     (982, 560), (994, 566), (1002, 570), (1014, 578), (1032, 588),
#     (1044, 594), (1052, 600), (1064, 606), (1076, 612), (1084, 618),
#     (1094, 624), (1106, 628), (1114, 634), (1126, 640), (1138, 644),
#     (1146, 650), (1158, 656), (1172, 660), (1180, 664), (1192, 670),
#     (1204, 676), (1224, 684), (1238, 690), (1250, 692), (1258, 698),
#     (1270, 702)
# ]
# print(len(points))
# # 绘制所有点
# for (x, y) in points:
#     cv2.circle(image, (x, y), 3, (0, 0, 255), -1)  # 红色点

# # 定义分组框
# group1 = {
#     'x1': 846.00, 'y1': 456.00, 'x2': 1032.00, 'y2': 588.00,
#     'label': '5', 'confidence': '0.90', 
#     'distance': '24.41 cm', 'camera_coords': '(8.17, 7.89, 25.00) cm'
# }

# group2 = {
#     'x1': 1044.00, 'y1': 594.00, 'x2': 1138.00, 'y2': 644.00,
#     'label': '5', 'confidence': '0.90', 
#     'distance': '19.41 cm', 'camera_coords': '(9.43, 7.82, 15.88) cm'
# }

# group3= {
#     'x1': 1146.00, 'y1': 650.00, 'x2': 1258.00, 'y2': 698.00,
#     'label': '5', 'confidence': '0.90', 
#     'distance': '15.88 cm', 'camera_coords': '(9.43, 7.82, 15.88) cm'
# }

# # 绘制分组框和标签
# def draw_group(group, color):
#     # 绘制矩形框
#     cv2.rectangle(image, (int(group['x1']), int(group['y1'])), 
#                  (int(group['x2']), int(group['y2'])), color, 2)
    
#     # 创建标签文本
#     label_text = f"Label: {group['label']}, Conf: {group['confidence']}"
#     distance_text = f"Distance: {group['distance']}"
#     coords_text = f"Cam Coords: {group['camera_coords']}"
    
#     # 计算文本位置
#     text_y = int(group['y1']) - 10 if group['y1'] > 30 else int(group['y2']) + 20
    
#     # 绘制标签文本
#     # cv2.putText(image, label_text, (int(group['x1']), text_y), 
#     #             cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
#     cv2.putText(image, distance_text, (int(group['x1']), text_y + 20), 
#                 cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
#     # cv2.putText(image, coords_text, (int(group['x1']), text_y + 40), 
#     #             cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

# # 用不同颜色绘制两个分组
# draw_group(group1, (0, 255, 0))  # 绿色
# draw_group(group2, (255, 0, 0))  # 蓝色
# draw_group(group3, (255, 255, 0))  # 蓝色
# x_min, x_max = (0, 1280)
# y_min, y_max = (420, 700)

# # 绘制矩形框 (红色，线宽2)
# cv2.rectangle(image, (x_min, y_min), (x_max, y_max), (128, 255, 0), 2)
# # 保存图像
# cv2.imwrite('points_with_groups.png', image)

# # 显示图像
# cv2.imshow('Points with Group Boxes', image)
# cv2.waitKey(0)
# cv2.destroyAllWindows()


import cv2
import numpy as np

# 创建一个白色背景的图像 (720高 x 1280宽)
image = np.ones((1200, 1600, 3), dtype=np.uint8) * 255

# # 定义点的列表
# points = [
#     (1052, 116), (1064, 112), (1076, 112), (1036, 132), (1046, 126),
#     (1066, 128), (1074, 124), (1018, 144), (1030, 142), (1050, 146),
#     (1060, 140), (1016, 158), (1032, 158), (1048, 158), (1058, 154),
#     (1006, 174), (1016, 174), (1032, 174), (1042, 170), (568, 190),
#     (566, 206), (564, 220), (564, 236), (566, 252), (568, 268),
#     (572, 284), (578, 302), (872, 300), (584, 316), (590, 328),
#     (594, 336), (600, 348), (892, 380), (896, 388), (600, 404),
#     (902, 398), (912, 404), (1048, 398), (568, 416), (582, 410),
#     (918, 414), (928, 422), (1062, 414), (572, 426), (582, 432),
#     (594, 436), (936, 428), (948, 436), (602, 442), (616, 446),
#     (630, 452), (640, 454), (956, 440), (966, 444), (982, 450),
#     (998, 454), (1010, 454), (1064, 452), (1078, 450), (638, 456),
#     (648, 458), (664, 462), (678, 468), (690, 470), (1004, 456),
#     (1016, 456), (1030, 456), (1046, 456), (1062, 456), (552, 486),
#     (564, 480), (698, 472), (712, 474), (728, 478), (744, 480),
#     (758, 484), (770, 486), (508, 500), (518, 496), (534, 494),
#     (548, 492), (778, 488), (792, 492), (808, 496), (822, 500),
#     (834, 502), (492, 514), (500, 508), (842, 506), (854, 510),
#     (868, 516), (494, 522), (502, 530), (860, 534), (868, 526),
#     (510, 536), (520, 540), (534, 546), (548, 550), (810, 550),
#     (824, 546), (838, 542), (852, 538), (556, 552), (568, 554),
#     (582, 556), (598, 558), (614, 560), (630, 560), (646, 562),
#     (662, 562), (678, 562), (694, 562), (710, 562), (726, 560),
#     (742, 560), (758, 558), (774, 556), (790, 552), (802, 552)
# ]

points = [
    (1315, 193), (1330, 186), (1345, 186), (1295, 220), (1307, 210),
    (1332, 213), (1342, 206), (1272, 240), (1287, 236), (1312, 243),
    (1325, 233), (1270, 263), (1290, 263), (1310, 263), (1322, 256),
    (1257, 290), (1270, 290), (1290, 290), (1302, 283), (710, 316),
    (707, 343), (705, 366), (705, 393), (707, 420), (710, 446),
    (715, 473), (722, 503), (1090, 500), (730, 526), (737, 546),
    (742, 560), (750, 580), (1115, 633), (1120, 646), (750, 673),
    (1127, 663), (1140, 673), (1310, 663), (710, 693), (727, 683),
    (1147, 690), (1160, 703), (1327, 690), (715, 710), (727, 720),
    (742, 726), (1170, 713), (1185, 726), (752, 736), (770, 743),
    (787, 753), (800, 756), (1195, 733), (1207, 740), (1227, 750),
    (1247, 756), (1262, 756), (1330, 753), (1347, 750), (797, 760),
    (810, 763), (830, 770), (847, 780), (862, 783), (1255, 760),
    (1270, 760), (1287, 760), (1307, 760), (1327, 760), (690, 810),
    (705, 800), (872, 786), (890, 790), (910, 796), (930, 800),
    (947, 806), (962, 810), (635, 833), (647, 826), (667, 823),
    (685, 820), (972, 813), (990, 820), (1010, 826), (1027, 833),
    (1042, 836), (615, 856), (625, 846), (1052, 843), (1067, 850),
    (1085, 860), (617, 870), (627, 883), (1075, 890), (1085, 876),
    (637, 893), (650, 900), (667, 910), (685, 916), (1012, 916),
    (1030, 910), (1047, 903), (1065, 896), (695, 920), (710, 923),
    (727, 926), (747, 930), (767, 933), (787, 933), (807, 936),
    (827, 936), (847, 936), (867, 936), (887, 936), (907, 933),
    (927, 933), (947, 930), (967, 926), (987, 920), (1002, 920)
]

print(len(points))
# 绘制所有点
for (x, y) in points:
    cv2.circle(image, (int(x), int(y)), 3, (0, 0, 255), -1)  # 红色点

# 定义分组框
group1 = {
    'x1': 1160.00, 'y1': 703.00, 'x2': 1347.00, 'y2': 760.00,
    'label': '5', 'confidence': '0.90', 
    'distance': '37.88 cm', 'camera_coords': '(8.17, 7.89, 25.00) cm'
}

group2 = {
    'x1': 847.00, 'y1': 780.00, 'x2': 1067.00, 'y2': 850.00,
    'label': '5', 'confidence': '0.90', 
    'distance': '27.45 cm', 'camera_coords': '(9.43, 7.82, 15.88) cm'
}

group3= {
    'x1': 615.00, 'y1': 710.00, 'x2': 830.00, 'y2': 883.00,
    'label': '5', 'confidence': '0.90', 
    'distance': '20.59 cm', 'camera_coords': '(9.43, 7.82, 15.88) cm'
}

group4= {
    'x1': 637.00, 'y1': 893.00, 'x2': 787.00, 'y2': 933.00,
    'label': '5', 'confidence': '0.90', 
    'distance': '18.39 cm', 'camera_coords': '(9.43, 7.82, 15.88) cm'
}

group5= {
    'x1': 807.00, 'y1': 860.00, 'x2': 1085.00, 'y2': 936.00,
    'label': '5', 'confidence': '0.90', 
    'distance': '18.12 cm', 'camera_coords': '(9.43, 7.82, 15.88) cm'
}


group6= {
    'x1': 615.00, 'y1': 800.00, 'x2': 705.00, 'y2': 884.00,
    'label': '5', 'confidence': '0.90', 
    'distance': '0 cm', 'camera_coords': '(9.43, 7.82, 15.88) cm'
}

group7= {
    'x1': 715.00, 'y1': 710.00, 'x2': 830.00, 'y2': 770.00,
    'label': '5', 'confidence': '0.90', 
    'distance': '0 cm', 'camera_coords': '(9.43, 7.82, 15.88) cm'
}

# 绘制分组框和标签
def draw_group(group, color):
    # 绘制矩形框
    cv2.rectangle(image, (int(group['x1']), int(group['y1'])), 
                 (int(group['x2']), int(group['y2'])), color, 2)
    
    # 创建标签文本
    label_text = f"Label: {group['label']}, Conf: {group['confidence']}"
    distance_text = f"Distance: {group['distance']}"
    coords_text = f"Cam Coords: {group['camera_coords']}"
    
    # 计算文本位置
    text_y = int(group['y1']) - 10 if group['y1'] > 30 else int(group['y2']) + 20
    
    # 绘制标签文本
    # cv2.putText(image, label_text, (int(group['x1']), text_y), 
    #             cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    cv2.putText(image, distance_text, (int(group['x1']), text_y + 20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    # cv2.putText(image, coords_text, (int(group['x1']), text_y + 40), 
    #             cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

# 用不同颜色绘制两个分组
draw_group(group1, (0, 255, 0))  # 绿色
draw_group(group2, (255, 0, 0))  # 蓝色
draw_group(group3, (255, 255, 0))  # 蓝色
draw_group(group4, (0, 0, 255))  # 蓝色
draw_group(group5, (255, 0, 255))  # 蓝色

draw_group(group6, (0, 128, 255))  # 蓝色
draw_group(group7, (0, 0, 0))  # 蓝色

x_min, x_max = (0, 1600)
y_min, y_max = (680, 1200)

# 绘制矩形框 (红色，线宽2)
cv2.rectangle(image, (x_min, y_min), (x_max, y_max), (128, 255, 0), 2)
# 保存图像
cv2.imwrite('points_with_groups.png', image)

# 显示图像
cv2.imshow('Points with Group Boxes', image)
cv2.waitKey(0)
cv2.destroyAllWindows()