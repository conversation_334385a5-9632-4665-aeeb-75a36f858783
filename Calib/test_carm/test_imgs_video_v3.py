# Copyright (c) OpenMMLab. All rights reserved.
import os
import sys
import random
import time
from pathlib import Path
from argparse import ArgumentParser

import cv2
import mmcv
import numpy as np
import torch
from PIL import Image
from typing import List, Tuple
import math

# Custom module imports
sys.path.append(str(Path(__file__).resolve().parents[3]))
sys.path.append('/home/<USER>/panpan/mm_detection/test_carm')

from projects.easydeploy.model import ORTWrapper  # noqa: E402
from utils import (  # noqa: E402
    bbox_postprocess, 
    preprocess, 
    visualize_detections,
    reg_max2bbox,
    resize_and_pad
)
from decode import (  # noqa: E402
    predict_by_feat,
    _bbox_post_process,
    get_single_pred,
    
)
from easydict import EasyDict
from pixel_to_physical_py.src.detection_processor import DetectionProcessor, DetectionResult, BBox
from pixel_to_physical_py.src.camera_coordinate_converter import CameraCoordinateConverter, CameraCoordinate

# Configuration and Constants
COLORS = [[random.randint(0, 255) for _ in range(3)] for _ in range(1000)]
CLASS_NAMES = ['bin', 'cloth', 'rug', 'shoe', 'wire', 'rail', 'wheel']
def transform_point(point, scale_factor, pad_param):
    x, y, conf = point
    top, bottom, left, right = pad_param
    x_actual = (x - left) / scale_factor
    y_actual = (y - top)  / scale_factor
    return (x_actual, y_actual, conf)

def parse_args():
    parser = ArgumentParser()
    parser.add_argument('--camera-id', type=int, default=0)
    parser.add_argument('--frame-size', nargs=2, type=int, default=[1280, 720])
    parser.add_argument('--config', default="yolov8s_old7cls_640.py")
    parser.add_argument('--checkpoint', '-c', default="yolov8v6_only.onnx")
    parser.add_argument('--line_config', default="line_in_five_head.py")
    parser.add_argument('--line_checkpoint', '-l', default="yolov8v13_only.onnx")
    parser.add_argument('--out-dir', default='camera_output')
    parser.add_argument('--device', default='cuda:0')
    parser.add_argument('--fps-display', action='store_true')
    # parser.add_argument('--outlier-mode', choices=['filter', 'correct'], default='filter')
    parser.add_argument('--window-size', type=int, default=10)
    parser.add_argument('--max_z_diff_threshold', type=float, default=14)
    parser.add_argument('--pixel-gap-threshold', type=float, default=45)
    parser.add_argument('--y-threshold', type=float, default=9.1)
    return parser.parse_args()

def load_model(checkpoint_path, device):
    if checkpoint_path.endswith('.onnx'):
        model = ORTWrapper(checkpoint_path, device)
    model.to(device)
    return model

def build_preprocessing_pipeline():
    return {
        'pipeline': resize_and_pad,
        'preprocessor': preprocess(),
    }

def preprocess_image(rgb_img, pipeline, preprocessor, device):
    processed = pipeline(rgb_img)
    data, scale, pad_param = processed
    scale = 1.0/scale
    data = preprocessor(data).to(device)
    samples = {"scale": scale, "pad_param": pad_param}
    return data, samples

def process_detection_results(feats, device, test_cfg, original_shape, scale_factor, pad_param):
    bbox_preds, cls_scores = [], []
    for feat in feats:
        bbox_pred, cls_score = torch.split(feat, [64, 7], dim=1)
        bbox_preds.append(bbox_pred)
        cls_scores.append(cls_score)
    if bbox_preds[0].shape[1] == 64:
        proj = torch.arange(16, dtype=torch.float).to(device)
        bbox_preds = [reg_max2bbox(bbox, proj) for bbox in bbox_preds]
    batch_img_metas = [{
        'ori_shape': original_shape,
        'pad_shape': (640, 640, 3),
        'scale_factor': scale_factor,
        'pad_param': pad_param
    }]
    return predict_by_feat(cls_scores, bbox_preds, objectnesses=None,
                           batch_img_metas=batch_img_metas, cfg=test_cfg,
                           post_processing=_bbox_post_process)

def _preprocess_image(img: np.ndarray, calibration_mapx_path, calibration_mapy_path) -> np.ndarray:
    calibration_mapx = np.fromfile(calibration_mapx_path, dtype=np.float32).reshape(img.shape[0], img.shape[1])
    calibration_mapy = np.fromfile(calibration_mapy_path, dtype=np.float32).reshape(img.shape[0], img.shape[1])
    processed_img = cv2.remap(img, calibration_mapx, calibration_mapy, cv2.INTER_LINEAR)
    print("使用标定映射表处理图像")
    return processed_img

def _initialize_physical_processor() -> DetectionProcessor:
    return DetectionProcessor()

def _initialize_camera_converter() -> CameraCoordinateConverter:
    camera_config_path = "pixel_to_physical_py/config/calib_intrix_new.yaml"
    return CameraCoordinateConverter(camera_config_path)

def _convert_points_to_detection_results(points_data: List[Tuple], point_size: int = 10) -> List[DetectionResult]:
    detection_results = []
    for i, (x_actual, y_actual, conf) in enumerate(points_data):
        bbox_obj = BBox(x1=int(x_actual), y1=int(y_actual), x2=int(x_actual), y2=int(y_actual))
        det_result = DetectionResult(bbox=bbox_obj, label=f"point_{i}", confidence=float(conf))
        detection_results.append(det_result)
    return detection_results


def euclidean(p1, p2):
    return math.hypot(p1[0] - p2[0], p1[1] - p2[1])

def find_endpoints_by_distance(points):
    # 基于所有点对的距离，找出最远的两个点作为端点
    max_dist = -1
    endpoint1, endpoint2 = None, None
    for i, p1 in enumerate(points):
        for j in range(i + 1, len(points)):
            p2 = points[j]
            d = euclidean(p1, p2)
            if d > max_dist:
                max_dist = d
                endpoint1, endpoint2 = p1, p2
    return endpoint1, endpoint2


def sort_points_greedy_index(points_with_conf):
    coords = [(float(p[0]), float(p[1]), float(p[2])) if len(p) == 3 else (float(p[0]), float(p[1])) for p in points_with_conf]
    points = [(x, y) for x, y, *_ in coords]
    if not points:
        return []

    # 找到两个端点（最远的两点）
    start, _ = find_endpoints_by_distance(points)

    # 初始化
    remaining = set(range(len(points)))
    start_idx = points.index(start)
    ordered_indices = [start_idx]
    remaining.remove(start_idx)

    while remaining:
        last_idx = ordered_indices[-1]
        last_point = points[last_idx]
        # 在剩下的点中找与当前点最近的
        next_idx = min(remaining, key=lambda i: euclidean(last_point, points[i]))
        ordered_indices.append(next_idx)
        remaining.remove(next_idx)

    return ordered_indices



# def group_valid_windows_single_merge(valid_results, window_size=10, max_z_diff_threshold=14, pixel_gap_threshold=45):
#     """
#     将不合法窗口或不足窗口的段，仅合并到“一个”最近合法窗口，防止重复合并。
#     """
#     from math import hypot

#     def euclidean(p1, p2):
#         return hypot(p1[0] - p2[0], p1[1] - p2[1])

#     def is_continuous(points):
#         return all(euclidean(points[i][1], points[i+1][1]) <= pixel_gap_threshold for i in range(len(points)-1))

#     def z_diff_within(points):
#         return abs(points[-1][0].physical_distance - points[0][0].physical_distance) <= max_z_diff_threshold

#     def create_box(points):
#         xs = [p[1][0] for p in points]
#         ys = [p[1][1] for p in points]
#         return (min(xs), min(ys), max(xs), max(ys), points[0][0].confidence)

#     def center_of_points(points):
#         xs = [p[1][0] for p in points]
#         ys = [p[1][1] for p in points]
#         return (sum(xs)/len(xs), sum(ys)/len(ys))

#     def split_window_if_not_nearby(points):
#         sub_windows = []
#         current = [points[0]]
#         for i in range(1, len(points)):
#             if euclidean(points[i][1], points[i-1][1]) <= pixel_gap_threshold:
#                 current.append(points[i])
#             else:
#                 sub_windows.append(current)
#                 current = [points[i]]
#         if current:
#             sub_windows.append(current)
#         return sub_windows

#     i = 0
#     groups = []
#     insufficient_segments = []

#     while i < len(valid_results):
#         window = valid_results[i:i+window_size]
#         if len(window) < window_size:
#             insufficient_segments.append(window)
#             break

#         if is_continuous(window):
#             valid_flag = z_diff_within(window)
#             if len(window) < window_size:
#                 insufficient_segments.append(window)
#             else:
#                 groups.append({'points': window, 'valid': valid_flag})
#             i += window_size
#         else:
#             sub_windows = split_window_if_not_nearby(window)
#             for sub in sub_windows:
#                 if len(sub) < window_size - 3:
#                     insufficient_segments.append(sub)
#                 else:
#                     valid_flag = z_diff_within(sub)
#                     groups.append({'points': sub, 'valid': valid_flag})
#             i += window_size

#     tail = valid_results[i:]
#     if tail:
#         tail_sub_windows = split_window_if_not_nearby(tail)
#         for sub in tail_sub_windows:
#             if len(sub) < window_size:
#                 insufficient_segments.append(sub)
#             else:
#                 groups.append({'points': sub, 'valid': z_diff_within(sub)})

#     # 合法框
#     legal_groups = [g for g in groups if g['valid']]
#     legal_centers = [center_of_points(g['points']) for g in legal_groups]

#     # 把每个不合法组单独合并进最近的一个合法框
#     for invalid_group in [g for g in groups if not g['valid']] + [{'points': s} for s in insufficient_segments]:
#         c = center_of_points(invalid_group['points'])
#         distances = [euclidean(c, lc) for lc in legal_centers]
#         if not distances:
#             print(f"[警告] 合法窗口为空或无效，跳过不合法段。")
#             continue
#         nearest_idx = distances.index(min(distances))
#         legal_groups[nearest_idx]['points'].extend(invalid_group['points'])

#     # 构建最终框
#     boxes = []
#     valid_point_indices = set()
#     for g in legal_groups:
#         box = create_box(g['points'])
#         boxes.append(box)
#         for p in g['points']:
#             idx = valid_results.index(p)
#             valid_point_indices.add(idx)

#     filtered_valid_results = [valid_results[i] for i in sorted(valid_point_indices)]
#     return boxes, filtered_valid_results

def group_valid_windows_single_merge(valid_results, window_size=10, max_z_diff_threshold=14, pixel_gap_threshold=45):
    from math import hypot

    def euclidean(p1, p2):
        return hypot(p1[0] - p2[0], p1[1] - p2[1])

    def is_continuous(points):
        return all(euclidean(points[i][1], points[i+1][1]) <= pixel_gap_threshold for i in range(len(points)-1))

    def z_diff_within(points):
        return abs(points[-1][0].physical_distance - points[0][0].physical_distance) <= max_z_diff_threshold

    def create_box(points):
        xs = [p[1][0] for p in points]
        ys = [p[1][1] for p in points]
        return (min(xs), min(ys), max(xs), max(ys), points[0][0].confidence)

    def center_of_points(points):
        xs = [p[1][0] for p in points]
        ys = [p[1][1] for p in points]
        return (sum(xs)/len(xs), sum(ys)/len(ys))

    def split_window_if_not_nearby(points):
        sub_windows = []
        current = [points[0]]
        for i in range(1, len(points)):
            if euclidean(points[i][1], points[i-1][1]) <= pixel_gap_threshold:
                current.append(points[i])
            else:
                sub_windows.append(current)
                current = [points[i]]
        if current:
            sub_windows.append(current)
        return sub_windows

    i = 0
    groups = []
    insufficient_segments = []

    while i < len(valid_results):
        window = valid_results[i:i+window_size]
        if len(window) < window_size:
            insufficient_segments.append(window)
            break

        if is_continuous(window):
            valid_flag = z_diff_within(window)
            if len(window) < window_size:
                insufficient_segments.append(window)
            else:
                groups.append({'points': window, 'valid': valid_flag})
            i += window_size
        else:
            sub_windows = split_window_if_not_nearby(window)
            for sub in sub_windows:
                if len(sub) < window_size - 3:
                    insufficient_segments.append(sub)
                else:
                    valid_flag = z_diff_within(sub)
                    groups.append({'points': sub, 'valid': valid_flag})
            i += window_size

    tail = valid_results[i:]
    if tail:
        tail_sub_windows = split_window_if_not_nearby(tail)
        for sub in tail_sub_windows:
            if len(sub) < window_size:
                insufficient_segments.append(sub)
            else:
                groups.append({'points': sub, 'valid': z_diff_within(sub)})

    legal_groups = [g for g in groups if g['valid']]

    # ⚠️ 使用最小点对距离进行最近合法框判断（而不是中心点距离）
    for invalid_group in [g for g in groups if not g['valid']] + [{'points': s} for s in insufficient_segments]:
        min_dist = float('inf')
        nearest_idx = -1
        for idx, legal_group in enumerate(legal_groups):
            for p1 in invalid_group['points']:
                for p2 in legal_group['points']:
                    dist = euclidean(p1[1], p2[1])
                    if dist < min_dist:
                        min_dist = dist
                        nearest_idx = idx
        if nearest_idx == -1:
            print(f"[警告] 无法找到合并目标，跳过。")
            continue
        legal_groups[nearest_idx]['points'].extend(invalid_group['points'])

    boxes = []
    valid_point_indices = set()
    for g in legal_groups:
        box = create_box(g['points'])
        boxes.append(box)
        for p in g['points']:
            idx = valid_results.index(p)
            valid_point_indices.add(idx)

    filtered_valid_results = [valid_results[i] for i in sorted(valid_point_indices)]
    return boxes, filtered_valid_results



def split_window_if_not_nearby(window_points, pixel_gap_threshold):
    """
    如果窗口内点不连续，则按连续段拆分
    返回多个子窗口，每个子窗口满足像素邻近
    """
    sub_windows = []
    current = [window_points[0]]
    for i in range(1, len(window_points)):
        prev = window_points[i - 1][1]
        curr = window_points[i][1]
        if euclidean(prev, curr) <= pixel_gap_threshold:
            current.append(window_points[i])
        else:
            if len(current) >= 2:
                sub_windows.append(current)
            current = [window_points[i]]
    if len(current) >= 2:
        sub_windows.append(current)
    return sub_windows


def _convert_boxes_to_detection_results(boxes_data: List[Tuple]) -> List[DetectionResult]:
    detection_results = []
    for i, (x1, y1, x2, y2, conf) in enumerate(boxes_data):
        bbox_obj = BBox(x1=int(x1), y1=int(y1), x2=int(x2), y2=int(y2))
        det_result = DetectionResult(bbox=bbox_obj, label=f"wire_{i}", confidence=float(conf))
        detection_results.append(det_result)
    return detection_results

def main():
    args = parse_args()
    cap = cv2.VideoCapture(args.camera_id)
    cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
    cap.set(cv2.CAP_PROP_FPS, 30)
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.frame_size[0])
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.frame_size[1])
    cv2.namedWindow("监控视角 - 检测结果", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("监控视角 - 检测结果", 1280, 720)

    cv2.namedWindow("监控视角 - 原图", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("监控视角 - 原图", 1280, 720)

    calibration_mapx = "pixel_to_physical_py/config/mapx"
    calibration_mapy = "pixel_to_physical_py/config/mapy"
    physical_processor = _initialize_physical_processor()
    camera_converter = _initialize_camera_converter()

    model = load_model(args.checkpoint, args.device)
    line_model = load_model(args.line_checkpoint, args.device)
    main_pp = build_preprocessing_pipeline()
    test_cfg = EasyDict(
        max_per_img=300,
        multi_label=True,
        nms=dict(iou_threshold=0.7, type='nms'),
        nms_pre=30000,
        score_thr=0.001)
    os.makedirs("output", exist_ok=True)
    frame_count, start_time = 0, time.time()
    print("开始实时检测，按ESC键退出...")
    while cap.isOpened():
        # Capture frame-by-frame
        ret, frame = cap.read()
        if not ret:
            print("无法获取帧，退出...")
            # continue
            break
        frame = cv2.imread("/home/<USER>/panpan/code/Calib/test_carm/21_real.png")
        rgb_frame = frame
        # rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        original_shape = rgb_frame.shape
        # rgb_frame = _preprocess_image(rgb_frame, calibration_mapx, calibration_mapy)
        # cv2.imwrite("output/"+str(frame_count)+"_real.png",rgb_frame)
        main_data, main_samples = preprocess_image(rgb_frame, main_pp['pipeline'], main_pp['preprocessor'], args.device)
        
        index_frame = rgb_frame.copy()
        main_result = model(main_data)
        main_results = process_detection_results(main_result, args.device, test_cfg, original_shape,
                                                    main_samples.get('scale', 1), main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32)))

        line_data, _ = preprocess_image(rgb_frame, main_pp['pipeline'], main_pp['preprocessor'], args.device)
        line_result = line_model(line_data)
        line_preds, line_scores = torch.split(line_result[0], [2, 1], dim=1)
        _, line_points, _ = get_single_pred(line_scores, line_preds, (640, 640))

        scale_factor = main_samples.get('scale', 1)
        pad_param = main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32))
        transformed_points = [transform_point(p, scale_factor, pad_param) for p in line_points if p[2] > 0.5]

        # 先绘制原始检测点（红色小圆点）
        for pt in transformed_points:
            cv2.circle(rgb_frame, (int(pt[0]), int(pt[1])), 5, (0, 0, 255), -1)

        org_frame = rgb_frame.copy()
        distance_table_path = "pixel_to_physical_py/config/distance_table"
        detection_results = _convert_points_to_detection_results(transformed_points)
        valid_results = []
        # print("detection_results: ", detection_results)
        # exit()
        for i, det_result in enumerate(detection_results):
            print("det_result: ", det_result.bbox)
            physical_processor.process_detection_result(det_result, distance_table_path)
            if det_result.physical_distance > 0:
                valid_results.append((det_result, transformed_points[i]))
        # exit()

        
        for i, (det_result, point_coords) in enumerate(valid_results):
            # 在圆圈内显示距离值
            point_x, point_y = int(point_coords[0]), int(point_coords[1])
            distance_value = int(det_result.physical_distance)  # 取整数，更简洁
            distance_text = f"{distance_value}"

            border_color = (255, 255, 255)  # 白色边框
            if det_result.physical_distance < 30:
                    circle_color = (0, 255, 0)  # 绿色 - 近距离
            elif det_result.physical_distance < 60:
                circle_color = (255, 255, 0)  # 黄色 - 中距离
            else:
                circle_color = (255, 0, 0)  # 红色 - 远距离
            
            # 圆圈大小和字体设置
            circle_radius = 8
            font_scale = 0.3

            # 绘制填充的圆圈作为背景
            cv2.circle(org_frame, (point_x, point_y), circle_radius, circle_color, -1)

            # # 绘制圆圈边框（修正过的点用不同颜色边框）
            # cv2.circle(org_frame, (point_x, point_y), circle_radius, border_color, 2)

            # 计算文字位置（居中）
            text_size = cv2.getTextSize(distance_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, 1)[0]
            text_x = point_x - text_size[0] // 2
            text_y = point_y + text_size[1] // 2

            # 在圆圈内绘制距离数字（黑色）
            cv2.putText(org_frame, distance_text, (text_x, text_y),
                        cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), 1)

        if len(valid_results) > 1:
            # 提取 transformed_points
            valid_transformed_points = [pt for (_, pt) in valid_results]
            # 获取排序后的索引
            sorted_indices = sort_points_greedy_index(valid_transformed_points)
            # 重新排序 valid_results
            valid_results = [valid_results[i] for i in sorted_indices]

            processed_results = valid_results

            boxes, filtered_valid_results = group_valid_windows_single_merge(processed_results, 
                                                                             window_size=args.window_size, 
                                                                             max_z_diff_threshold=args.max_z_diff_threshold,
                                                                             pixel_gap_threshold=args.pixel_gap_threshold)
            # 使用过滤后的点进行后续处理
            processed_results = filtered_valid_results
            box_detection_results = _convert_boxes_to_detection_results(boxes)
            box_valid_results = []
            for box_det_result in box_detection_results:
                physical_processor.process_detection_result(box_det_result, distance_table_path)
                if box_det_result.physical_distance > 0:
                    box_valid_results.append(box_det_result)
            # print(len(box_valid_results))
            # exit()
            for box_det_result in box_valid_results:
                bbox = box_det_result.bbox
                cv2.rectangle(rgb_frame, (bbox.x1, bbox.y1), (bbox.x2, bbox.y2), (0, 255, 0), 2)
                
                text_size = cv2.getTextSize(f"{box_det_result.physical_distance:.1f}cm", cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
                text_x = bbox.x1
                text_y = bbox.y1 - 10
                cv2.rectangle(rgb_frame, (text_x, text_y-15), (text_x + text_size[0], text_y+10), (227,218,251), -1)
                cv2.putText(rgb_frame, f"{box_det_result.physical_distance:.1f}cm", (text_x, text_y),
                            cv2.FONT_HERSHEY_TRIPLEX, 0.5, (0, 0, 0), 1)
               
            # # 可视化距离值
            print(f"\n=== 可视化 {len(processed_results)} 个检测点的距离值 ===")
            for i, (det_result, point_coords) in enumerate(processed_results):
                # # 在圆圈内显示距离值
                point_x, point_y = int(point_coords[0]), int(point_coords[1])
                distance_value = int(det_result.physical_distance)  # 取整数，更简洁
                distance_text = f"{distance_value}"

                # 根据距离选择颜色
                if distance_value < 30:
                    circle_color = (0, 255, 0)  # 绿色 - 近距离
                elif distance_value < 60:
                    circle_color = (255, 255, 0)  # 黄色 - 中距离
                else:
                    circle_color = (255, 0, 0)  # 红色 - 远距离

                border_color = (255, 255, 255)  # 白色边框

                # 圆圈和字体设置
                circle_radius = 8
                font_scale = 0.3

                # 绘制填充的圆圈作为背景
                cv2.circle(rgb_frame, (point_x, point_y), circle_radius, circle_color, -1)

                # 计算文字位置（居中）
                text_size = cv2.getTextSize(distance_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, 1)[0]
                text_x = point_x - text_size[0] // 2
                text_y = point_y + text_size[1] // 2

                # # 在圆圈内绘制距离数字（黑色）
                cv2.putText(rgb_frame, distance_text, (text_x, text_y),
                            cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), 1)
    
    # 在去畸变图像上绘制范围框
        x_min, x_max = (0, 1280)
        y_min, y_max = (420, 700)
        
        # 绘制矩形框 (红色，线宽2)
        cv2.rectangle(rgb_frame, (x_min, y_min), (x_max, y_max), (128, 255, 0), 2)
        cv2.rectangle(org_frame, (x_min, y_min), (x_max, y_max), (128, 255, 0), 2)
        display_img_org = cv2.cvtColor(org_frame, cv2.COLOR_RGB2BGR)
        cv2.imwrite("output/"+str(frame_count)+"_org.png",display_img_org)
        # Convert back to BGR for OpenCV display
        display_img = cv2.cvtColor(rgb_frame, cv2.COLOR_RGB2BGR)
        cv2.imwrite("output/"+str(frame_count)+"_correct.png",display_img)
        # Calculate and display FPS
        frame_count += 1
        if args.fps_display and frame_count % 10 == 0:
            fps = frame_count / (time.time() - start_time)
            cv2.putText(display_img, f"FPS: {fps:.2f}", (20, 50), 
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        cv2.imshow("监控视角 - 检测结果", display_img)
        cv2.imshow("监控视角 - 原图", display_img_org)
        # Exit on ESC key
        key = cv2.waitKey(1)
        if key == 27:  # ESC key
            print("ESC键按下，退出程序...")
            break
    
    # Cleanup
    cap.release()
    cv2.destroyAllWindows()
    print("程序已结束")
    

if __name__ == '__main__':
    main()

